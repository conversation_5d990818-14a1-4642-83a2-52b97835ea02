import * as crypto from 'crypto';

/**
 * Generate a secure random password that meets the system requirements
 * @param length Password length (default: 12)
 * @returns A secure random password
 */
export function generateSecurePassword(length: number = 12): string {
  // Define character sets
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const specialChars = '@$!%*?&';
  
  // Ensure minimum requirements are met
  if (length < 8) {
    length = 8;
  }
  
  // Start with one character from each required set
  let password = '';
  password += getRandomChar(lowercase);
  password += getRandomChar(uppercase);
  password += getRandomChar(numbers);
  password += getRandomChar(specialChars);
  
  // Fill the rest with random characters from all sets
  const allChars = lowercase + uppercase + numbers + specialChars;
  for (let i = 4; i < length; i++) {
    password += getRandom<PERSON>har(allChars);
  }
  
  // Shuffle the password to avoid predictable patterns
  return shuffleString(password);
}

/**
 * Get a random character from a string
 * @param chars String of characters to choose from
 * @returns A random character
 */
function getRandomChar(chars: string): string {
  const randomIndex = crypto.randomInt(0, chars.length);
  return chars[randomIndex];
}

/**
 * Shuffle a string randomly
 * @param str String to shuffle
 * @returns Shuffled string
 */
function shuffleString(str: string): string {
  const array = str.split('');
  
  // Fisher-Yates shuffle algorithm
  for (let i = array.length - 1; i > 0; i--) {
    const j = crypto.randomInt(0, i + 1);
    [array[i], array[j]] = [array[j], array[i]];
  }
  
  return array.join('');
}

/**
 * Validate if a password meets the system requirements
 * @param password Password to validate
 * @returns True if password meets requirements, false otherwise
 */
export function validatePasswordStrength(password: string): boolean {
  if (!password || password.length < 8) {
    return false;
  }
  
  const hasLowercase = /[a-z]/.test(password);
  const hasUppercase = /[A-Z]/.test(password);
  const hasNumber = /\d/.test(password);
  const hasSpecialChar = /[@$!%*?&]/.test(password);
  
  return hasLowercase && hasUppercase && hasNumber && hasSpecialChar;
}
