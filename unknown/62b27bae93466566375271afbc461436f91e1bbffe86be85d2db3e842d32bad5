import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AwardsService } from './awards.service';
import { AwardsController } from './awards.controller';
import { Award } from '../../database/entities/award.entity';
import { AwardWinner } from '../../database/entities/award-winner.entity';
import { RewardPoint } from '../../database/entities/reward-point.entity';
import { AwardSchedule } from '../../database/entities/award-schedule.entity';
import { User } from '../../database/entities/user.entity';
import { CommonModule } from '../../common/common.module';
import { JwtService } from '@nestjs/jwt';
import { AwardScheduleService } from './award-schedule.service';
import { AwardScheduleScheduler } from './award-schedule.scheduler';
import { AwardScheduleController } from './award-schedule.controller';
import { DiaryModule } from '../diary/diary.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Award, AwardWinner, RewardPoint, User, AwardSchedule]),
    CommonModule,
    forwardRef(() => DiaryModule),
  ],
  controllers: [AwardsController, AwardScheduleController],
  providers: [
    {
      provide: AwardsService,
      useClass: AwardsService,
    },
    {
      provide: AwardScheduleService,
      useClass: AwardScheduleService,
    },
    {
      provide: AwardScheduleScheduler,
      useClass: AwardScheduleScheduler,
    },
    JwtService,
  ],
  exports: [AwardsService, AwardScheduleService,AwardScheduleScheduler],
})
export class AwardsModule {}
