import { Controller, Get, HttpCode, HttpStatus, Body, Post, BadRequestException } from '@nestjs/common';
import {  ApiBearerAuth, ApiBody, ApiOperation, ApiTags  } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { CreateRoleDto, ForgotPasswordDto, ForgotUserIdDto, LoginUserDto, RegisterDto, ResendVerificationEmailDto, ResetPasswordDto, VerifyEmailDto } from '../../database/models/users.dto';
import { LoginResponseDto } from '../../database/models/auth-response.dto';
import { UserType } from 'src/database/entities/user.entity';
import { Public } from 'src/common/decorators/public-api.decorator';
import { Roles } from 'src/common/decorators/roles.decorator';
import { ApiResponse } from 'src/common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse } from 'src/common/decorators/api-response.decorator';
@ApiTags('auth')
@ApiBearerAuth('JWT-auth')
@Controller('auth')
export class AuthController {

    constructor(private readonly authService: AuthService) {}

    @Post('register')
    @Public()
    @HttpCode(HttpStatus.CREATED)
    @ApiOperation({
        summary: 'Register a new student or tutor user',
        description: 'Register a new student or tutor user. Required fields: userId (will also be used as name), email, phone, gender, password, confirmPassword, and agreement to terms and conditions. A verification link will be sent to the provided email. For tutor registration, the account will require admin approval after email verification. Admin users can only be created by an existing admin.'
    })
    @ApiBody({
        type: RegisterDto,
        description: 'User registration data',
        examples: {
            student: {
                summary: 'Student Registration',
                description: 'Register as a student',
                value: {
                    userId: 'new_student',
                    email: '<EMAIL>',
                    phoneNumber: '+**********',
                    gender: 'male',
                    password: 'Student@123',
                    confirmPassword: 'Student@123',
                    agreedToTerms: true,
                    type: 'student'
                }
            },
            tutor: {
                summary: 'Tutor Registration',
                description: 'Register as a tutor (requires admin approval after email verification)',
                value: {
                    userId: 'new_tutor',
                    email: '<EMAIL>',
                    phoneNumber: '+**********',
                    gender: 'female',
                    password: 'Tutor@123',
                    confirmPassword: 'Tutor@123',
                    agreedToTerms: true,
                    type: 'tutor',
                    bio: 'I have 5 years of experience teaching English.'
                }
            }
        }
    })
    @ApiOkResponseWithType(RegisterDto, 'User registered successfully and verification link sent to email')
    @ApiErrorResponse(400, 'Invalid input data', ['Email must be a valid email address', 'Password must be at least 8 characters long'])
    @ApiErrorResponse(409, 'User already exists', 'Email or userId already in use')
    @ApiErrorResponse(500, 'An error occurred during registration')
    async register(@Body() registerDto: RegisterDto): Promise<ApiResponse<{ userId: string }>> {
        try {
            const result = await this.authService.register(registerDto);
            return ApiResponse.success({ userId: result.userId }, 'Registration successful. Please check your email for verification link.', 201);
        } catch (error) {
            // Log the detailed error for debugging
            console.error('Registration error details:', JSON.stringify(error.response || error.message));

            // If it's a BadRequestException with validation details, return those details
            if (error instanceof BadRequestException && error.getResponse() && typeof error.getResponse() === 'object') {
                const response = error.getResponse() as any;
                if (response.error && response.error.details) {
                    throw new BadRequestException({
                        message: response.message || 'Registration validation failed',
                        error: response.error
                    });
                }
            }

            // Re-throw the original error
            throw error;
        }
    }
    @Post('verify-email')
    @Public()
    @HttpCode(HttpStatus.OK)
    @ApiOperation({
        summary: 'Verify email address',
        description: 'Verify the user\'s email address using the token sent in the verification email.'
    })
    @ApiBody({
        type: VerifyEmailDto,
        description: 'Email verification data',
        examples: {
            verifyEmail: {
                summary: 'Email Verification',
                description: 'Verify email using token',
                value: {
                    token: 'abc123def456'
                }
            }
        }
    })
    @ApiOkResponseWithType(Object, 'Email verified successfully')
    @ApiErrorResponse(400, 'Invalid or expired verification token')
    @ApiErrorResponse(404, 'User not found')
    @ApiErrorResponse(500, 'An error occurred during email verification')
    async verifyEmail(@Body() verifyEmailDto: VerifyEmailDto): Promise<ApiResponse<any>> {
        const result = await this.authService.verifyEmail(verifyEmailDto.token);
        return ApiResponse.success(result, 'Email verified successfully');
    }

    @Post('login')
    @Public()
    @HttpCode(HttpStatus.OK)
    @ApiOperation({
        summary: 'Unified user login',
        description: 'Login with userId and password (not display name). The userId is the unique identifier used during registration and cannot be changed. If selectedRole is not provided, the system will automatically use the highest role available to the user. The rememberMe option extends the session duration to 30 days. You can specify a returnUrl to redirect to after successful login (defaults to /home). Note: Tutors must be approved by an administrator before they can log in.'
    })
    @ApiBody({
        type: LoginUserDto,
        description: 'Login data',
        examples: {
            withoutRole: {
                summary: 'Login (Automatic Role)',
                description: 'Login without specifying a role (system will use highest available role)',
                value: {
                    userId: 'admin',
                    password: '123456_Az',
                    rememberMe: true,
                    returnUrl: '/admin/dashboard'
                }
            },
            student: {
                summary: 'Student Login',
                description: 'Login as a student (seeded account)',
                value: {
                    userId: 'student',
                    password: 'Student@123',
                    selectedRole: 'student',
                    rememberMe: false,
                    returnUrl: '/student/diary'
                }
            },
            additionalStudent: {
                summary: 'Additional Student Login',
                description: 'Login as one of the additional seeded students',
                value: {
                    userId: 'john_doe',
                    password: 'Student@123',
                    selectedRole: 'student',
                    rememberMe: false,
                    returnUrl: '/student/plans'
                }
            },
            tutor: {
                summary: 'Tutor Login',
                description: 'Login as a tutor (seeded account). Note: Tutors must be approved by an administrator before they can log in.',
                value: {
                    userId: 'tutor',
                    password: 'Tutor@123',
                    selectedRole: 'tutor',
                    rememberMe: false,
                    returnUrl: '/tutor/dashboard'
                }
            },
            admin: {
                summary: 'Admin Login',
                description: 'Login as an admin (seeded account)',
                value: {
                    userId: 'admin',
                    password: '123456_Az',
                    selectedRole: 'admin',
                    rememberMe: true,
                    returnUrl: '/admin/users'
                }
            }
        }
    })
    @ApiOkResponseWithType(LoginResponseDto, 'Login successful')
    @ApiErrorResponse(401, 'Invalid credentials, unconfirmed account, inactive account, pending tutor approval, or incorrect role')
    @ApiErrorResponse(500, 'An error occurred during login')
    async login(@Body() loginDto: LoginUserDto): Promise<ApiResponse<LoginResponseDto>> {
        const result = await this.authService.login(loginDto);
        return ApiResponse.success(result, 'Login successful');
    }



    @Get('roles')
    @Public()
    @ApiOperation({
        summary: 'Get all roles',
        description: 'Get a list of all available roles in the system.'
    })
    @ApiOkResponseWithType(Object, 'List of roles retrieved successfully')
    async roles(): Promise<ApiResponse<any[]>> {
        const roles = await this.authService.getRoles();
        return ApiResponse.success(roles, 'Roles retrieved successfully');
    }

    @Post('create-role')
    @Roles(UserType.ADMIN)
    @ApiOperation({
        summary: 'Create a new role',
        description: 'Create a new role in the system.'
    })
    @ApiBody({
        type: CreateRoleDto,
        description: 'Role creation data',
        examples: {
            role: {
                summary: 'Create Role',
                description: 'Create a new role',
                value: {
                    name: 'moderator'
                }
            }
        }
    })
    @ApiOkResponseWithType(Object, 'Role created successfully')
    @ApiErrorResponse(409, 'Role already exists', 'Role with name already exists')
    async createRole(@Body() createRoleDto: CreateRoleDto): Promise<ApiResponse<any>> {
        const result = await this.authService.createRole(createRoleDto);
        return ApiResponse.success(result, 'Role created successfully', 201);
    }

    @Post('forgot-password')
    @Public()
    @HttpCode(HttpStatus.OK)
    @ApiOperation({
        summary: 'Request password reset link',
        description: 'Request a password reset link to be sent to the user\'s email. The link will be valid for 5 minutes. You can provide either an email address or a userId in the identifier field.'
    })
    @ApiBody({
        type: ForgotPasswordDto,
        description: 'Forgot password data',
        examples: {
            forgotPasswordByEmail: {
                summary: 'Forgot Password (By Email)',
                description: 'Request password reset link using email',
                value: {
                    identifier: '<EMAIL>'
                }
            },
            forgotPasswordByUserId: {
                summary: 'Forgot Password (By User ID)',
                description: 'Request password reset link using user ID',
                value: {
                    identifier: 'john123'
                }
            }
        }
    })
    @ApiOkResponseWithType(Object, 'Password reset link sent to email if account exists')
    @ApiErrorResponse(500, 'Internal server error')
    async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto): Promise<ApiResponse<null>> {
        await this.authService.forgotPassword(forgotPasswordDto);
        return ApiResponse.success(null, 'If your email is registered, you will receive a password reset link.');
    }

    @Post('forgot-userid')
    @Public()
    @HttpCode(HttpStatus.OK)
    @ApiOperation({
        summary: 'Request user ID recovery',
        description: 'Request your user ID to be sent to your registered email.'
    })
    @ApiBody({
        type: ForgotUserIdDto,
        description: 'Forgot user ID data',
        examples: {
            forgotUserId: {
                summary: 'Forgot User ID',
                description: 'Request user ID recovery',
                value: {
                    email: '<EMAIL>'
                }
            }
        }
    })
    @ApiOkResponseWithType(Object, 'User ID sent to email if account exists')
    @ApiErrorResponse(500, 'Internal server error')
    async forgotUserId(@Body() forgotUserIdDto: ForgotUserIdDto): Promise<ApiResponse<null>> {
        await this.authService.forgotUserId(forgotUserIdDto);
        return ApiResponse.success(null, 'If your email is registered, you will receive your User ID.');
    }

    @Post('reset-password')
    @Public()
    @HttpCode(HttpStatus.OK)
    @ApiOperation({
        summary: 'Reset password using token',
        description: 'Reset the user\'s password using the token from the password reset link.'
    })
    @ApiBody({
        type: ResetPasswordDto,
        description: 'Reset password data',
        examples: {
            resetPassword: {
                summary: 'Reset Password',
                description: 'Reset password using token from reset link',
                value: {
                    token: 'abc123def456',
                    newPassword: 'NewStrongP@ss123'
                }
            }
        }
    })
    @ApiOkResponseWithType(Object, 'Password reset successful')
    @ApiErrorResponse(400, 'Invalid or expired token', 'Invalid or expired password reset token')
    @ApiErrorResponse(404, 'User not found')
    @ApiErrorResponse(500, 'An error occurred while resetting your password')
    async resetPassword(@Body() resetPasswordDto: ResetPasswordDto): Promise<ApiResponse<null>> {
        await this.authService.resetPassword(resetPasswordDto);
        return ApiResponse.success(null, 'Password has been reset successfully. You can now login with your new password.');
    }

    @Post('resend-verification-email')
    @Public()
    @HttpCode(HttpStatus.OK)
    @ApiOperation({
        summary: 'Resend verification email',
        description: 'Resend the verification email if the previous link has expired. A new verification link will be sent to the provided email address.'
    })
    @ApiBody({
        type: ResendVerificationEmailDto,
        description: 'Resend verification email data',
        examples: {
            resendVerificationEmail: {
                summary: 'Resend Verification Email',
                description: 'Request a new verification link',
                value: {
                    email: '<EMAIL>'
                }
            }
        }
    })
    @ApiOkResponseWithType(Object, 'Verification email resent successfully')
    @ApiErrorResponse(400, 'Email already verified', 'Your email is already verified. You can now login to your account.')
    @ApiErrorResponse(500, 'An error occurred while processing your request')
    async resendVerificationEmail(@Body() resendVerificationEmailDto: ResendVerificationEmailDto): Promise<ApiResponse<null>> {
        const result = await this.authService.resendVerificationEmail(resendVerificationEmailDto);
        return ApiResponse.success(null, result.message);
    }
}
