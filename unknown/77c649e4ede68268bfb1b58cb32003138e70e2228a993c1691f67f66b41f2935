import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsIn } from 'class-validator';
import { PaginationDto } from '../../../common/models/pagination.dto';

/**
 * DTO for filtering and paginating users
 */
export class UserFilterDto extends PaginationDto {
  @ApiProperty({
    description: 'Filter by user ID (partial match)',
    example: 'admin',
    required: false
  })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty({
    description: 'Filter by email (partial match)',
    example: 'example.com',
    required: false
  })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiProperty({
    description: 'Filter by phone number (partial match)',
    example: '123',
    required: false
  })
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @ApiProperty({
    description: 'Filter by gender',
    example: 'male',
    enum: ['male', 'female', 'other'],
    required: false
  })
  @IsOptional()
  @IsString()
  @IsIn(['male', 'female', 'other'], { each: false })
  gender?: string;

  @ApiProperty({
    description: 'Field to sort by',
    example: 'userId',
    enum: ['userId', 'email', 'phoneNumber', 'gender', 'createdAt', 'name'],
    required: false
  })
  @IsOptional()
  @IsString()
  @IsIn(['userId', 'email', 'phoneNumber', 'gender', 'createdAt', 'name'], { each: false })
  override sortBy?: string = 'createdAt';
}
