import { Injectable, BadRequestException, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThanOrEqual } from 'typeorm';
import { DiaryEntry, DiaryEntryStatus } from '../../database/entities/diary-entry.entity';
import { DiaryAward, DiaryAwardPeriod } from '../../database/entities/diary-award.entity';
import { DiaryEntryAttendance, AttendanceStatus } from '../../database/entities/diary-entry-attendance.entity';
import { AwardsService } from '../awards/awards.service';
import { AwardModule, AwardCriteria, AwardFrequency } from '../../database/entities/award.entity';
import { getCurrentUTCDate, addDaysUTC, addMonthsUTC } from '../../common/utils/date-utils';
import {
  DiaryAwardsResponseDto,
  DiaryAwardSummaryDto,
  DiaryTopScorersResponseDto,
  DiaryPeriodAwardsResponseDto
} from '../../database/models/diary.dto';

@Injectable()
export class DiaryAwardService {
  private readonly logger = new Logger(DiaryAwardService.name);

  constructor(
    @InjectRepository(DiaryEntry)
    private diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(DiaryAward)
    private diaryAwardRepository: Repository<DiaryAward>,
    @InjectRepository(DiaryEntryAttendance)
    private diaryEntryAttendanceRepository: Repository<DiaryEntryAttendance>,
    @Inject(forwardRef(() => AwardsService))
    private readonly awardsService: AwardsService
  ) {}

  /**
   * Get awards for a student
   * @param userId The ID of the student
   * @returns The student's awards
   */
  async getStudentAwards(userId: string): Promise<DiaryAwardsResponseDto> {
    // Get all awards for the student
    const awards = await this.diaryAwardRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });

    // Group awards by name and count them
    const awardCounts = new Map<string, number>();
    for (const award of awards) {
      const count = awardCounts.get(award.awardTitle) || 0;
      awardCounts.set(award.awardTitle, count + 1);
    }

    // Convert to response DTO
    const awardSummaries: DiaryAwardSummaryDto[] = Array.from(awardCounts.entries()).map(([awardTitle, count]) => ({
      awardName: awardTitle, // Map awardTitle to awardName for the response DTO
      count,
    }));

    return {
      awards: awardSummaries,
      totalAwards: awards.length,
    };
  }

  /**
   * Get period awards for a student
   * @param userId The ID of the student
   * @returns The student's period awards
   */
  async getStudentPeriodAwards(userId: string): Promise<DiaryPeriodAwardsResponseDto> {
    // Get all period awards for the student
    const awards = await this.diaryAwardRepository.find({
      where: { userId, period: DiaryAwardPeriod.WEEKLY },
      order: { periodStartDate: 'DESC' },
    });

    // No need to group by period since we're just returning the awards directly

    // Map awards to the correct format for the response DTO
    const mappedAwards = awards.slice(0, 5).map(award => ({
      id: award.id,
      userId: award.userId,
      userName: 'Unknown', // We don't have user name in the award entity
      period: award.period,
      periodStartDate: award.periodStartDate,
      periodEndDate: award.periodEndDate,
      totalScore: award.totalScore,
      awardTitle: award.awardTitle,
      awardDescription: award.awardDescription,
      createdAt: award.createdAt,
    }));

    return {
      awards: mappedAwards
    };
  }

  /**
   * Get top scorers for a period
   * @param period The period to get top scorers for
   * @returns The top scorers for the period
   */
  async getTopScorers(period: DiaryAwardPeriod): Promise<DiaryTopScorersResponseDto> {
    // Calculate the start and end dates for the period
    const now = getCurrentUTCDate();
    let startDate: Date;
    let endDate = now;
    let periodName: string;

    switch (period) {
      case DiaryAwardPeriod.WEEKLY:
        // Start date is 7 days ago
        startDate = addDaysUTC(now, -7);
        periodName = 'Weekly';
        break;
      case DiaryAwardPeriod.MONTHLY:
        // Start date is 1 month ago
        startDate = addMonthsUTC(now, -1);
        periodName = 'Monthly';
        break;
      // YEARLY is not defined in DiaryAwardPeriod enum
      // case DiaryAwardPeriod.YEARLY:
      //   // Start date is 1 year ago
      //   startDate = addYearsUTC(now, -1);
      //   periodName = 'Yearly';
      //   break;
      default:
        throw new BadRequestException(`Invalid period: ${period}`);
    }

    // Get all confirmed entries within the period
    const entries = await this.diaryEntryRepository.find({
      where: {
        status: DiaryEntryStatus.CONFIRM,
        entryDate: Between(startDate, endDate),
        score: MoreThanOrEqual(1), // Only include entries with a score
      },
      relations: ['diary', 'diary.user'],
    });

    // Group entries by user and calculate scores
    const userScores = new Map<string, { userId: string, userName: string, totalScore: number, entriesCount: number }>();

    for (const entry of entries) {
      const userId = entry.diary.userId;
      const userName = entry.diary.user?.name || 'Unknown User';

      const userScore = userScores.get(userId) || { userId, userName, totalScore: 0, entriesCount: 0 };
      userScore.totalScore += entry.score || 0;
      userScore.entriesCount += 1;

      userScores.set(userId, userScore);
    }

    // Convert to array and sort by total score
    const topScorers = Array.from(userScores.values())
      .map(score => ({
        userId: score.userId,
        userName: score.userName,
        totalScore: score.totalScore,
        entriesCount: score.entriesCount,
        averageScore: score.entriesCount > 0 ? score.totalScore / score.entriesCount : 0,
      }))
      .sort((a, b) => b.totalScore - a.totalScore)
      .slice(0, 10); // Get top 10

    return {
      topScorers,
      period: periodName,
      startDate,
      endDate,
    };
  }

  /**
   * Generate awards for top scorers (DEPRECATED - Use generateAwardsForRange instead)
   * @param period The period to generate awards for
   * @deprecated Use generateAwardsForRange instead which provides more flexibility with date ranges
   */
  async generatePeriodAwards(period: DiaryAwardPeriod): Promise<void> {
    // For backward compatibility, convert period to date range
    const now = getCurrentUTCDate();
    let startDate: Date;
    const endDate = now;

    switch (period) {
      case DiaryAwardPeriod.WEEKLY:
        startDate = addDaysUTC(now, -7);
        break;
      case DiaryAwardPeriod.MONTHLY:
        startDate = addMonthsUTC(now, -1);
        break;
      case DiaryAwardPeriod.QUARTERLY:
        startDate = addMonthsUTC(now, -3);
        break;
      default:
        throw new BadRequestException(`Invalid period: ${period}`);
    }

    // Call the new method with date range
    await this.generateAwardsForRange(startDate, endDate);
  }

  /**
   * Generate awards for a specific date range
   * This is the preferred method for generating awards as it provides more flexibility
   * @param startDate Start date of the period
   * @param endDate End date of the period
   */
  async generateAwardsForRange(startDate: Date, endDate: Date): Promise<void> {
    try {
      // Get available awards for the diary module
      const availableAwards = await this.awardsService.getAllAwards(
        AwardModule.DIARY, 
        false, // only active awards
        undefined,
        undefined,
        this.determineFrequency(startDate, endDate)
      );

      // Get top scorers for the period
      const topScorersResponse = await this.getTopScorersForRange(startDate, endDate);
      
      // Check if awards have already been generated for this period
      const existingAwards = await this.diaryAwardRepository.find({
        where: {
          periodStartDate: startDate,
          periodEndDate: endDate,
        },
      });

      if (existingAwards.length > 0) {
        this.logger.log(`Awards already exist for period from ${startDate.toISOString()} to ${endDate.toISOString()}`);
        return;
      }

      const allScorers = topScorersResponse.topScorers;
      if (allScorers.length === 0) {
        this.logger.log('No eligible scorers found for the period');
        return;
      }

      const period = this.determinePeriod(startDate, endDate);

      // Process each award separately
      for (const award of availableAwards.items) {
        // Calculate scores for all scorers
        const scorerResults = await Promise.all(
          allScorers.map(async (scorer) => {
            let meetsMinimumCriteria = true;
            let totalScore = 0;

            // Get attendance records
            const attendanceRecords = await this.diaryEntryAttendanceRepository
              .createQueryBuilder('attendance')
              .where('attendance.studentId = :userId', { userId: scorer.userId })
              .andWhere('attendance.entryDate BETWEEN :startDate AND :endDate', { startDate, endDate })
              .andWhere('attendance.status = :status', { status: AttendanceStatus.PRESENT })
              .getMany();

            // Get confirmed entries with scores
            const scorerEntries = await this.diaryEntryRepository
              .createQueryBuilder('entry')
              .where('entry.diary.userId = :userId', { userId: scorer.userId })
              .andWhere('entry.entryDate BETWEEN :startDate AND :endDate', { startDate, endDate })
              .andWhere('entry.status = :status', { status: DiaryEntryStatus.CONFIRM })
              .getMany();

            const totalAttendance = attendanceRecords.length;
            const avgEntryScore = scorerEntries.length > 0 
              ? scorerEntries.reduce((sum, entry) => sum + (entry.score || 0), 0) / scorerEntries.length 
              : 0;            // Calculate total likes for this scorer
            const totalLikes = scorerEntries.reduce((sum, entry) => sum + (entry.likes ? entry.likes.length : 0), 0);

            // Check each criterion
            for (const criterion of award.criteria) {
              switch (criterion) {                case AwardCriteria.DIARY_SCORE: {
                  const minScore = award.criteriaConfig?.minScore || 0;
                  const minEntries = award.criteriaConfig?.entriesRequired || 0;
                  const targetEntries = award.criteriaConfig?.targetEntries || minEntries;
                  
                  if (scorerEntries.length < minEntries || avgEntryScore < minScore) {
                    meetsMinimumCriteria = false;
                    break;
                  }
                  
                  // Calculate quality score (0-100) based on average entry score
                  const qualityScore = Math.min(avgEntryScore, 100);
                  
                  // Calculate quantity bonus (0-100) based on number of entries
                  // If entries exceed target, they get bonus points up to 100
                  const quantityRatio = Math.min(scorerEntries.length / targetEntries, 2); // Cap at 200% of target
                  const quantityScore = Math.min(quantityRatio * 50, 100); // Max 100 points for quantity
                  
                  // Combine quality and quantity with weights
                  // Quality is weighted 60%, quantity 40%
                  const combinedScore = (qualityScore * 0.6) + (quantityScore * 0.4);
                  totalScore += combinedScore;
                  break;
                }
                
                case AwardCriteria.ATTENDANCE: {
                  const daysRequired = award.criteriaConfig?.daysRequired || 0;
                  if (totalAttendance < daysRequired) {
                    meetsMinimumCriteria = false;
                    break;
                  }
                  
                  // Calculate attendance ratio and convert to 0-100 score
                  const attendanceRatio = Math.min(totalAttendance / daysRequired, 1);
                  totalScore += attendanceRatio * 100;
                  break;
                }

                case AwardCriteria.DIARY_DECORATION: {
                  const minLikes = award.criteriaConfig?.minLikes || 0;
                  if (totalLikes < minLikes) {
                    meetsMinimumCriteria = false;
                    break;
                  }

                  // Calculate likes ratio and convert to 0-100 score
                  const likesRatio = Math.min(totalLikes / minLikes, 1);
                  totalScore += likesRatio * 100;
                  break;
                }
              }
              
              if (!meetsMinimumCriteria) break;
            }

            // Average the scores from all criteria
            totalScore = award.criteria.length > 0 ? totalScore / award.criteria.length : 0;
            
            return {
              scorer,
              totalScore: meetsMinimumCriteria ? totalScore : -1,              metrics: {
                totalDiaryScore: scorer.totalScore,
                confirmedEntries: scorerEntries.length,
                averageEntryScore: avgEntryScore,
                totalAttendanceDays: totalAttendance,
                totalLikes: totalLikes
              }
            };
          })
        );

        // Filter out non-qualifying scorers and sort by total score
        const qualifiedScorers = scorerResults
          .filter(result => result.totalScore >= 0)
          .sort((a, b) => b.totalScore - a.totalScore);

        // Select top performers based on configuration
        const maxWinners = award.criteriaConfig?.maxWinners || 1;
        const winners = qualifiedScorers.slice(0, maxWinners);

        // Create awards for winners
        for (const winner of winners) {
          try {
            await this.awardsService.createAwardWinner({
              userId: winner.scorer.userId,
              awardId: award.id,
              awardDate: endDate.toISOString().split('T')[0],              awardReason: `${award.description} - Average Entry Score: ${Math.round(winner.metrics.averageEntryScore)}, 
                Confirmed Entries: ${winner.metrics.confirmedEntries}, 
                Attendance: ${winner.metrics.totalAttendanceDays} days,
                Total Likes: ${winner.metrics.totalLikes}`,
              metadata: {
                ...winner.metrics,
                combinedScore: winner.totalScore,
                period: period,
                periodStartDate: startDate,
                periodEndDate: endDate
              }
            });

            this.logger.log(`Created award winner: User ${winner.scorer.userId} for award ${award.name} with score ${Math.round(winner.totalScore)}`);
          } catch (error) {
            this.logger.error(`Error creating award winner for user ${winner.scorer.userId}, award ${award.name}: ${error.message}`);
          }
        }
      }
    } catch (error) {
      this.logger.error(`Error generating awards: ${error.message}`, error.stack);
      throw new Error('Failed to generate awards');
    }
  }

  /**
   * Get top scorers for a specific date range
   * @param startDate Start date of the period
   * @param endDate End date of the period
   * @returns The top scorers for the period
   */
  async getTopScorersForRange(startDate: Date, endDate: Date): Promise<DiaryTopScorersResponseDto> {
    try {
      // Get all confirmed diary entries within the date range
      const entries = await this.diaryEntryRepository.find({
        where: {
          status: DiaryEntryStatus.CONFIRM,
          entryDate: Between(startDate, endDate),
          score: MoreThanOrEqual(1) // Only include entries with a score
        },
        relations: ['diary', 'diary.user'],
        order: {
          score: 'DESC'
        }
      });

      // Group entries by user and calculate totals
      const userScores = new Map<string, { 
        userId: string; 
        userName: string; 
        totalScore: number; 
        entriesCount: number;
      }>();      
      
      for (const entry of entries) {
        const userId = entry.diary.userId;
        const userName = entry.diary.user?.name || 'Unknown User';
        const userScore = userScores.get(userId) || { 
          userId, 
          userName, 
          totalScore: 0, 
          entriesCount: 0 
        };
        
        userScore.totalScore += entry.score ?? 0;
        userScore.entriesCount += 1;
        userScores.set(userId, userScore);
      }

      const periodDays = Math.ceil(
        (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
      );
      const period = periodDays <= 7 ? 'Weekly' : 
                    periodDays <= 31 ? 'Monthly' :
                    periodDays <= 93 ? 'Quarterly' : 'Yearly';

      // Convert to array and sort by total score
      const topScorers = Array.from(userScores.values())
        .map(score => ({
          userId: score.userId,
          userName: score.userName,
          totalScore: score.totalScore,
          entriesCount: score.entriesCount,
          averageScore: score.entriesCount > 0 ? score.totalScore / score.entriesCount : 0
        }))
        .sort((a, b) => b.totalScore - a.totalScore);      return {
        startDate,
        endDate,
        period,
        topScorers: topScorers.slice(0, 10) // Get top 10 scorers
      };
    } catch (error) {
      this.logger.error(`Error getting top scorers: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get top scorers');
    }
  }

  /**
   * Generate awards for a specific date range
   * @param startDate Start date of the period
   * @param endDate End date of the period
   */  // Method implementation moved up to avoid duplication

  /**
   * Determine award frequency based on date range
   */  private determineFrequency(startDate: Date, endDate: Date): AwardFrequency {
    const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (days <= 7) return AwardFrequency.WEEKLY;
    if (days <= 31) return AwardFrequency.MONTHLY;
    return AwardFrequency.YEARLY; // Use YEARLY for anything longer than a month for now
  }

  /**
   * Determine award period based on date range
   */
  private determinePeriod(startDate: Date, endDate: Date): DiaryAwardPeriod {
    const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    return days <= 7 ? DiaryAwardPeriod.WEEKLY : DiaryAwardPeriod.MONTHLY;
  }
}
