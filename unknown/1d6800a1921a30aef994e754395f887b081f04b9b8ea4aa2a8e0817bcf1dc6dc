import { Injectable, NotFoundException, BadRequestException, Logger, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ShopItem, ShopItemType } from '../../database/entities/shop-item.entity';
import { PurchaseStatus } from '../../database/entities/shop-item-purchase.entity';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { ShopCategoryService } from './shop-category.service';
import { ShopFileService } from './shop-file.service';
import { ShopPurchaseService } from './shop-purchase.service';
import { ShopSkinService } from './shop-skin.service';
import { ShopItemService } from './shop-item.service';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { PromotionsService } from '../promotions/promotions.service';
import { PlansService } from '../plans/plans.service';
// We're using 'any' type for file uploads to avoid Express namespace issues
import {
  CreateShopCategoryDto,
  UpdateShopCategoryDto,
  ShopCategoryResponseDto,
  CreateShopItemDto,
  UpdateShopItemDto,
  ShopItemResponseDto,
  ShopItemWithPromotionDto,
  BulkPriceUpdateDto,
  BulkDiscountUpdateDto,
  ApplyPromotionDto,
  PurchaseShopItemDto,
  ShopItemPurchaseResponseDto,
  GenerateItemNumberDto,
  GroupedShopItemsResponseDto,
  ApplyPromotionToItemsDto
} from '../../database/models/shop.dto';
@Injectable()
export class ShopService {
  private readonly logger = new Logger(ShopService.name);

  constructor(
    @InjectRepository(ShopItem)
    private readonly shopItemRepository: Repository<ShopItem>,
    private readonly fileRegistryService: FileRegistryService,
    private readonly shopCategoryService: ShopCategoryService,
    private readonly shopFileService: ShopFileService,
    private readonly shopPurchaseService: ShopPurchaseService,
    private readonly shopSkinService: ShopSkinService,
    private readonly shopItemService: ShopItemService,
    private readonly promotionsService: PromotionsService,
    private readonly plansService: PlansService
  ) {}

  /**
   * Create a new shop category
   * @param createShopCategoryDto Category creation data
   * @returns Created category
   */
  async createShopCategory(createShopCategoryDto: CreateShopCategoryDto): Promise<ShopCategoryResponseDto> {
    return this.shopCategoryService.createShopCategory(createShopCategoryDto);
  }

  /**
   * Get all shop categories
   * @param includeInactive Whether to include inactive categories
   * @param parentId Optional parent ID to filter by
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of categories
   */
  async getAllShopCategories(includeInactive: boolean = false, parentId?: string, paginationDto?: PaginationDto): Promise<PagedListDto<ShopCategoryResponseDto>> {
    return this.shopCategoryService.getAllShopCategories(includeInactive, parentId, paginationDto);
  }

  /**
   * Get shop category by ID
   * @param id Category ID
   * @returns Category details
   */
  async getShopCategoryById(id: string): Promise<ShopCategoryResponseDto> {
    return this.shopCategoryService.getShopCategoryById(id);
  }

  /**
   * Update a shop category
   * @param id Category ID
   * @param updateShopCategoryDto Category update data
   * @returns Updated category
   */
  async updateShopCategory(id: string, updateShopCategoryDto: UpdateShopCategoryDto): Promise<ShopCategoryResponseDto> {
    return this.shopCategoryService.updateShopCategory(id, updateShopCategoryDto);
  }

  /**
   * Delete a shop category
   * @param id Category ID
   * @returns Success message
   */
  async deleteShopCategory(id: string): Promise<{ success: boolean; message: string }> {
    return this.shopCategoryService.deleteShopCategory(id);
  }

  /**
   * Generate a unique item number
   * @param generateItemNumberDto Item number generation data
   * @returns Generated item number
   */
  async generateItemNumber(generateItemNumberDto: GenerateItemNumberDto): Promise<string> {
    return this.shopItemService.generateItemNumber(generateItemNumberDto);
  }

  /**
   * Create a new shop item with file upload in one step
   * @param file File to upload
   * @param createShopItemDto Item creation data
   * @returns Created item
   */
  async createShopItemWithFile(file: any, createShopItemDto: CreateShopItemDto): Promise<ShopItemResponseDto> {
    return this.shopItemService.createShopItemWithFile(file, createShopItemDto);
  }

  /**
   * Create a new shop item
   * @param createShopItemDto Item creation data
   * @returns Created item
   */
  async createShopItem(createShopItemDto: CreateShopItemDto): Promise<ShopItemResponseDto> {
    return this.shopItemService.createShopItem(createShopItemDto);
  }

  /**
   * Get all shop items
   * @param categoryId Optional category ID to filter by
   * @param type Optional type to filter by
   * @param includeInactive Whether to include inactive items
   * @param featuredOnly Whether to only include featured items
   * @param itemNumber Optional item number to filter by
   * @param title Optional title to filter by
   * @param promotionId Optional promotion ID to filter by
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of shop items
   */
  async getAllShopItems(
    categoryId?: string,
    type?: ShopItemType,
    includeInactive: boolean = false,
    featuredOnly: boolean = false,
    itemNumber?: string,
    title?: string,
    promotionId?: string,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<ShopItemResponseDto>> {
    this.logger.log(
      `ShopService.getAllShopItems called with: categoryId=${categoryId}, type=${type}, includeInactive=${includeInactive}, featuredOnly=${featuredOnly}, itemNumber=${itemNumber}, title=${title}, promotionId=${promotionId}`
    );

    // Note: The isActive parameter is inverted from includeInactive
    // If includeInactive is true, we don't filter by isActive
    // If includeInactive is false, we filter for isActive=true
    const searchParams = {
      categoryId,
      type,
      isActive: includeInactive ? undefined : true,
      isFeatured: featuredOnly || undefined,
      itemNumber,
      search: title,
      promotionId
    };

    this.logger.log(`Calling shopItemService.getAllShopItems with searchParams: ${JSON.stringify(searchParams)}`);

    return this.shopItemService.getAllShopItems(searchParams, paginationDto);
  }

  /**
   * Get all shop items grouped by category
   * @param includeInactive Whether to include inactive items
   * @param title Optional title to filter items by (partial match)
   * @returns Grouped shop items by category
   */
  async getAllShopItemsGroupedByCategory(includeInactive: boolean = false, title?: string): Promise<GroupedShopItemsResponseDto> {
    return this.shopItemService.getAllShopItemsGroupedByCategory(!includeInactive, title);
  }
  /**
   * Get shop items by category
   * @param categoryId Category ID
   * @param includeInactive Whether to include inactive items
   * @param itemNumber Optional item number to filter by
   * @param title Optional title to filter by
   * @param promotionId Optional promotion ID to filter by
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of shop items
   */  async getShopItemsByCategory(
    categoryId: string,
    includeInactive: boolean = false,
    itemNumber?: string,
    title?: string,
    promotionId?: string,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<ShopItemResponseDto>> {
    return this.shopItemService.getShopItemsByCategory(
      categoryId,
      includeInactive,
      itemNumber,
      title,
      promotionId,
      paginationDto
    );
  }

  /**
   * Get shop item by ID
   * @param id Shop item ID
   * @returns Shop item details
   */
  async getShopItemById(id: string): Promise<ShopItemResponseDto> {
    return this.shopItemService.getShopItemById(id);
  }

  /**
   * Update a shop item with file upload
   * @param id Shop item ID
   * @param file Optional file to upload
   * @param updateShopItemDto Shop item update data
   * @returns Updated shop item
   */
  async updateShopItemWithFile(id: string, file: any, updateShopItemDto: UpdateShopItemDto): Promise<ShopItemResponseDto> {
    return this.shopItemService.updateShopItemWithFile(id, file, updateShopItemDto);
  }

  /**
   * Update a shop item
   * @param id Shop item ID
   * @param updateShopItemDto Shop item update data
   * @returns Updated shop item
   */
  async updateShopItem(id: string, updateShopItemDto: UpdateShopItemDto): Promise<ShopItemResponseDto> {
    return this.shopItemService.updateShopItem(id, updateShopItemDto);
  }

  /**
   * Delete a shop item
   * @param id Shop item ID
   * @returns Success message
   */
  async deleteShopItem(id: string): Promise<{ success: boolean; message: string }> {
    return this.shopItemService.deleteShopItem(id);
  }

  /**
   * Update prices for multiple shop items
   * @param bulkPriceUpdateDto Bulk price update data
   * @returns Success message
   */
  async bulkUpdatePrices(bulkPriceUpdateDto: BulkPriceUpdateDto): Promise<{ success: boolean; message: string }> {
    return this.shopItemService.bulkUpdatePrices(bulkPriceUpdateDto);
  }

  /**
   * Update discounted prices for multiple shop items
   * @param bulkDiscountUpdateDto Bulk discount update data
   * @returns Success message
   */
  async bulkUpdateDiscounts(bulkDiscountUpdateDto: BulkDiscountUpdateDto): Promise<{ success: boolean; message: string }> {
    return this.shopItemService.bulkUpdateDiscounts(bulkDiscountUpdateDto);
  }

  /**
   * Apply a promotion to multiple shop items
   * @param applyPromotionToItemsDto Promotion application data
   * @returns Success message
   */
  async applyPromotionToItems(applyPromotionToItemsDto: ApplyPromotionToItemsDto): Promise<{ success: boolean; message: string }> {
    return this.shopItemService.applyPromotionToItems(applyPromotionToItemsDto);
  }

  /**
   * Remove promotion from a shop item
   * @param itemId Shop item ID
   * @returns Success message
   */
  async removePromotionFromItem(itemId: string): Promise<{ success: boolean; message: string }> {
    return this.shopItemService.removePromotionFromItem(itemId);
  }

  /**
   * Get shop items with promotion details
   * @param categoryId Optional category ID to filter by
   * @param type Optional type to filter by
   * @param includeInactive Whether to include inactive items
   * @param featuredOnly Whether to only include featured items
   * @param searchTerm Optional search term for implicit filtering across item number, title, category name, and promotion name
   * @param promotionId Optional promotion ID to filter by
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of shop items with promotion details
   */
  async getShopItemsWithPromotions(
    categoryId?: string,
    type?: ShopItemType,
    includeInactive: boolean = false,
    featuredOnly: boolean = false,
    searchTerm?: string,
    promotionId?: string,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<ShopItemWithPromotionDto>> {
    this.logger.log(
      `ShopService.getShopItemsWithPromotions called with: categoryId=${categoryId}, type=${type}, includeInactive=${includeInactive}, featuredOnly=${featuredOnly}, searchTerm=${searchTerm}, promotionId=${promotionId}`
    );

    // Build search params without search term first
    const searchParams = {
      categoryId,
      type,
      isActive: includeInactive ? undefined : true,
      isFeatured: featuredOnly || undefined,
      promotionId
    };

    this.logger.log(`Initial searchParams: ${JSON.stringify(searchParams)}`);

    // Get all items with promotions first
    const result = await this.shopItemService.getShopItemsWithPromotions(searchParams, paginationDto);

    // If searchTerm is provided, filter the results manually to implement full-text search
    if (searchTerm && result.items.length > 0) {
      this.logger.log(`Filtering results with searchTerm: ${searchTerm}`);

      // Determine if we should do exact matching or partial matching
      const isItemNumber = searchTerm.includes('-');
      const isLongEnough = searchTerm.length >= 3;

      // Filter the items based on the search term
      const filteredItems = result.items.filter((item) => {
        // For item numbers with hyphens, do exact matching
        if (isItemNumber && item.itemNumber && item.itemNumber.toLowerCase() === searchTerm.toLowerCase()) {
          return true;
        }

        // For other search terms, require at least 3 characters for partial matching
        if (isLongEnough) {
          const searchTermLower = searchTerm.toLowerCase();

          // Check item number (partial match)
          if (item.itemNumber && item.itemNumber.toLowerCase().includes(searchTermLower)) {
            return true;
          }

          // Check title (partial match)
          if (item.title && item.title.toLowerCase().includes(searchTermLower)) {
            return true;
          }

          // Check category name (partial match)
          if (item.categoryName && item.categoryName.toLowerCase().includes(searchTermLower)) {
            return true;
          }

          // Check promotion name (partial match)
          if (item.promotionName && item.promotionName.toLowerCase().includes(searchTermLower)) {
            return true;
          }
        }

        return false;
      });

      this.logger.log(`Found ${filteredItems.length} items matching searchTerm "${searchTerm}" out of ${result.items.length} total items with promotions`);

      return new PagedListDto(filteredItems, filteredItems.length);
    }

    return result;
  }

  /**
   * Purchase a shop item
   * @param userId User ID making the purchase
   * @param purchaseShopItemDto Purchase data
   * @returns Purchase details
   */
  async purchaseShopItem(userId: string, purchaseShopItemDto: PurchaseShopItemDto): Promise<ShopItemPurchaseResponseDto> {
    return this.shopPurchaseService.purchaseShopItem(userId, purchaseShopItemDto);
  }

  /**
   * Get purchases for a user
   * @param userId User ID
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of purchases
   */
  async getUserPurchases(userId: string, paginationDto?: PaginationDto): Promise<PagedListDto<ShopItemPurchaseResponseDto>> {
    return this.shopPurchaseService.getUserPurchases(userId, paginationDto);
  }

  /**
   * Get purchase by ID
   * @param id Purchase ID
   * @returns Purchase details
   */
  async getPurchaseById(id: string): Promise<ShopItemPurchaseResponseDto> {
    return this.shopPurchaseService.getPurchaseById(id);
  }

  /**
   * Get all purchases (admin only)
   * @param status Optional status filter
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of purchases
   */
  async getAllPurchases(status?: PurchaseStatus, paginationDto?: PaginationDto): Promise<PagedListDto<ShopItemPurchaseResponseDto>> {
    return this.shopPurchaseService.getAllPurchases(status, paginationDto);
  }

  /**
   * Check if a user has purchased a specific shop item
   * @param userId User ID
   * @param shopItemId Shop item ID
   * @returns Whether the user has purchased the item
   */
  async hasUserPurchasedItem(userId: string, shopItemId: string): Promise<boolean> {
    return this.shopPurchaseService.hasUserPurchasedItem(userId, shopItemId);
  }

  /**
   * Get shop items available for purchase (not owned by the student)
   * @param userId User ID
   * @param categoryId Optional category ID to filter by
   * @param type Optional type to filter by
   * @param featuredOnly Whether to only include featured items
   * @param itemNumber Optional item number to filter by
   * @param title Optional title to filter by
   * @param promotionId Optional promotion ID to filter by
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of shop items available for purchase
   */
  async getShopItemsAvailableForPurchase(
    userId: string,
    categoryId?: string,
    type?: ShopItemType,
    featuredOnly: boolean = false,
    itemNumber?: string,
    title?: string,
    promotionId?: string,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<ShopItemResponseDto>> {
    this.logger.log(`ShopService.getShopItemsAvailableForPurchase called for userId=${userId}`);

    // Get all active shop items
    const allItems = await this.getAllShopItems(
      categoryId,
      type,
      false, // includeInactive = false (only active items)
      featuredOnly,
      itemNumber,
      title,
      promotionId,
      paginationDto
    );

    // If there are no items, return empty result
    if (allItems.items.length === 0) {
      return allItems;
    }

    // Get all item IDs
    const itemIds = allItems.items.map((item) => item.id);

    // Check which items the user has already purchased
    const purchasePromises = itemIds.map((itemId) =>
      this.hasUserPurchasedItem(userId, itemId)
        .then((hasPurchased) => ({ itemId, hasPurchased }))
        .catch(() => ({ itemId, hasPurchased: false }))
    );

    const purchaseResults = await Promise.all(purchasePromises);

    // Create a map of purchased items for quick lookup
    const purchasedItemsMap = purchaseResults.reduce((map, result) => {
      if (result.hasPurchased) {
        map[result.itemId] = true;
      }
      return map;
    }, {});

    // Filter out items that are already purchased (except free items which are always available)
    const availableItems = allItems.items.filter((item) => item.type === ShopItemType.FREE || !purchasedItemsMap[item.id]);

    // Add isFreeOrPurchased flag to each item
    availableItems.forEach((item) => {
      item['isFreeOrPurchased'] = item.type === ShopItemType.FREE;
    });

    // Return the filtered items
    return new PagedListDto(availableItems, availableItems.length);
  }

  /**
   * Get shop items available for purchase grouped by category
   * @param userId User ID
   * @param type Optional type to filter by
   * @param featuredOnly Whether to only include featured items
   * @returns Shop items grouped by category
   */
  async getAvailableItemsGroupedByCategory(
    userId: string,
    type?: ShopItemType,
    featuredOnly: boolean = false
  ): Promise<{ [categoryId: string]: { categoryName: string; items: ShopItemResponseDto[] } }> {
    this.logger.log(`ShopService.getAvailableItemsGroupedByCategory called for userId=${userId}`);

    // Get all categories
    const categoriesResponse = await this.shopCategoryService.getAllShopCategories();
    const categories = categoriesResponse.items;

    // Initialize result object
    const result: { [categoryId: string]: { categoryName: string; items: ShopItemResponseDto[] } } = {};

    // For each category, get available items
    for (const category of categories) {
      // Skip inactive categories
      if (!category.isActive) continue;

      // Get available items for this category
      const availableItems = await this.getShopItemsAvailableForPurchase(
        userId,
        category.id,
        type,
        featuredOnly,
        null, // itemNumber
        null, // title
        null, // promotionId
        null // paginationDto - we want all items for grouping
      );

      // Only add categories that have available items
      if (availableItems.items.length > 0) {
        result[category.id] = {
          categoryName: category.name,
          items: availableItems.items
        };
      }
    }

    return result;
  }

  /**
   * Get shop items available for purchase by category
   * @param userId User ID
   * @param categoryId Category ID
   * @param type Optional type to filter by
   * @param featuredOnly Whether to only include featured items
   * @param itemNumber Optional item number to filter by
   * @param title Optional title to filter by
   * @param promotionId Optional promotion ID to filter by
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of shop items available for purchase in the specified category
   */
  async getAvailableItemsByCategory(
    userId: string,
    categoryId: string,
    type?: ShopItemType,
    featuredOnly: boolean = false,
    itemNumber?: string,
    title?: string,
    promotionId?: string,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<ShopItemResponseDto>> {
    this.logger.log(`ShopService.getAvailableItemsByCategory called for userId=${userId}, categoryId=${categoryId}`);

    // Verify the category exists
    const category = await this.shopCategoryService.getShopCategoryById(categoryId);
    if (!category) {
      throw new NotFoundException(`Category with ID ${categoryId} not found`);
    }

    // Get available items for this category
    return this.getShopItemsAvailableForPurchase(userId, categoryId, type, featuredOnly, itemNumber, title, promotionId, paginationDto);
  }

  /**
   * Update purchase status (admin only)
   * @param id Purchase ID
   * @param status New status
   * @param notes Optional notes
   * @returns Updated purchase
   */
  async updatePurchaseStatus(id: string, status: PurchaseStatus, notes?: string): Promise<ShopItemPurchaseResponseDto> {
    return this.shopPurchaseService.updatePurchaseStatus(id, status, notes);
  }

  /**
   * Upload a file for a shop item
   * @param file File to upload
   * @param itemNumber Item number for reference
   * @param categoryId Optional category ID for organizing files
   * @param userId Optional user ID who uploaded the file
   * @returns Path to the uploaded file and registry entry
   * @deprecated Use FileRegistryService.uploadFile directly
   */
  async uploadShopItemFile(file: any, itemNumber: string, categoryId?: string, userId?: string): Promise<{ filePath: string; registry: any }> {
    return this.shopItemService.uploadShopItemFile(file, itemNumber, categoryId, userId);
  }

  /**
   * Create a shop item from a diary skin
   * @param diarySkinId ID of the diary skin
   * @param createShopItemDto Shop item creation data
   * @returns Created shop item
   */
  async createShopItemFromDiarySkin(diarySkinId: string, createShopItemDto: CreateShopItemDto): Promise<ShopItemResponseDto> {
    return this.shopSkinService.createShopItemFromDiarySkin(diarySkinId, createShopItemDto);
  }

  /**
   * Get the diary skin associated with a shop item
   * @param shopItemId Shop item ID
   * @returns Diary skin ID
   */
  async getDiarySkinIdForShopItem(shopItemId: string): Promise<string> {
    return this.shopSkinService.getDiarySkinIdForShopItem(shopItemId);
  }

  /**
   * Apply a purchased skin to a user's diary
   * @param userId User ID
   * @param shopItemId Shop item ID
   * @returns Success message
   */
  async applyPurchasedSkinToDiary(userId: string, shopItemId: string): Promise<{ success: boolean; message: string }> {
    return this.shopSkinService.applyPurchasedSkinToDiary(userId, shopItemId);
  }

  /**
   * Apply a promotion to multiple targets (items, categories, plans)
   * @param applyPromotionDto Promotion application data containing targets
   * @returns Result summary
   */
  async applyPromotion(applyPromotionDto: ApplyPromotionDto): Promise<{
    itemsUpdated?: number;
    itemsSkipped?: number;
    categoriesProcessed?: number;
    plansUpdated?: number;
    plansSkipped?: number;
    totalItemsAffected: number;
  }> {
    this.logger.log(`Applying promotion: ${JSON.stringify(applyPromotionDto)}`);

    // Initialize counters
    const result = {
      itemsUpdated: 0,
      itemsSkipped: 0,
      categoriesProcessed: 0,
      plansUpdated: 0,
      plansSkipped: 0,
      totalItemsAffected: 0
    };

    try {
      // Check if promotion exists and get its details
      await this.promotionsService.getPromotionById(applyPromotionDto.promotionId);

      // Process direct item IDs if provided
      if (applyPromotionDto.itemIds?.length > 0) {
        const itemResult = await this.shopItemService.applyPromotionToItems({
          promotionId: applyPromotionDto.promotionId,
          itemIds: applyPromotionDto.itemIds
        });

        if (itemResult.success) {
          result.itemsUpdated = applyPromotionDto.itemIds.length;
          result.totalItemsAffected += result.itemsUpdated;
        }
      }

      // Process category IDs if provided
      if (applyPromotionDto.categoryIds?.length > 0) {
        // For each category, get all active items and apply promotion
        for (const categoryId of applyPromotionDto.categoryIds) {
          try {
            // Get all active items in the category
            const categoryItems = await this.getShopItemsByCategory(categoryId, false);
            result.categoriesProcessed++;

            if (categoryItems.items.length > 0) {
              const itemIds = categoryItems.items.map(item => item.id);
              const categoryResult = await this.shopItemService.applyPromotionToItems({
                promotionId: applyPromotionDto.promotionId,
                itemIds
              });

              if (categoryResult.success) {
                result.itemsUpdated += itemIds.length;
                result.totalItemsAffected += itemIds.length;
              }
            }
          } catch (error) {
            this.logger.error(`Error processing category ${categoryId}: ${error.message}`);
            result.itemsSkipped++;
          }
        }
      }     

      // Process plan IDs if provided
      if (applyPromotionDto.planIds?.length > 0) {
        try {
          const planResult = await this.plansService.applyPromotionToPlans({
            promotionId: applyPromotionDto.promotionId,
            planIds: applyPromotionDto.planIds
          });

          if (planResult.success) {
            result.plansUpdated = applyPromotionDto.planIds.length;
            result.totalItemsAffected += result.plansUpdated;
          }
        } catch (error) {
          this.logger.error(`Error applying promotion to plans: ${error.message}`);
          result.plansSkipped = applyPromotionDto.planIds.length;
        }
      }

      // Validate that at least one target was processed
      if (result.totalItemsAffected === 0) {
        throw new BadRequestException('No items or plans were affected by the promotion');
      }

      return result;
    } catch (error) {
      this.logger.error(`Error applying promotion: ${error.message}`);
      throw new BadRequestException(`Failed to apply promotion: ${error.message}`);
    }
  }

  /**
   * Update promotion activation for a shop item
   * @param id Shop item ID 
   * @param isPromotionActive Whether the promotion should be active
   * @returns Updated shop item
   */
  async updatePromotionActivation(id: string, isPromotionActive: boolean): Promise<ShopItemResponseDto> {
    // Check if shop item exists
    const shopItem = await this.shopItemRepository.findOne({
      where: { id },
      relations: ['category']
    });

    if (!shopItem) {
      this.logger.error(`Shop item with ID ${id} not found`);
      throw new NotFoundException(`Shop item with ID ${id} not found`);
    }

    // Check if item has a promotion assigned
    if (!shopItem.promotionId) {
      this.logger.error(`Shop item with ID ${id} has no promotion assigned`);
      throw new ConflictException(`Shop item with ID ${id} has no promotion assigned`);
    }

    // Update promotion activation status
    shopItem.isPromotionActive = isPromotionActive;

    // Save updated shop item
    const savedItem = await this.shopItemRepository.save(shopItem);
    this.logger.log(`Promotion activation for shop item ${id} ${isPromotionActive ? 'enabled' : 'disabled'} successfully`);

    // Return mapped DTO with category name if available
    return this.mapShopItemToDto(savedItem, shopItem.category?.name);
  }

  /**
   * Map shop item entity to DTO
   * @param shopItem Shop item entity
   * @param categoryName Optional category name
   * @returns Shop item DTO
   */
  private mapShopItemToDto(shopItem: ShopItem, categoryName?: string): ShopItemResponseDto {
    return {
      id: shopItem.id,
      itemNumber: shopItem.itemNumber,
      title: shopItem.title,
      description: shopItem.description,
      categoryId: shopItem.categoryId,
      categoryName: categoryName,
      type: shopItem.type,
      price: Number(shopItem.price),
      isPurchasableInRewardpoint: shopItem.isPurchasableInRewardpoint,
      filePath: shopItem.filePath,
      isActive: shopItem.isActive,
      isFeatured: shopItem.isFeatured,
      shopItemCategory: categoryName?.toLowerCase() || null,
      promotionId: shopItem.promotionId,
      isPromotionActive: shopItem.isPromotionActive,
      discountedPrice: shopItem.discountedPrice ? Number(shopItem.discountedPrice) : null,
      finalPrice: shopItem.getFinalPrice(),
      isOnSale: shopItem.isOnSale(),
      discountPercentage: shopItem.getDiscountPercentage(),
      metadata: shopItem.metadata,
      purchaseCount: shopItem.purchaseCount,
      viewCount: shopItem.viewCount,
      createdAt: shopItem.createdAt,
      updatedAt: shopItem.updatedAt
    };
  }


  /**
   * Generate a direct URL for a shop item file
   * @param shopItemId Shop item ID
   * @returns Direct URL for accessing the file
   */
  async getSecureFileUrl(shopItemId: string): Promise<string> {
    return this.shopFileService.getShopItemFileUrl(shopItemId);
  }
}
