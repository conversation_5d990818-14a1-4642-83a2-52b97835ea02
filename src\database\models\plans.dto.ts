import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsNumber, IsEnum, IsBoolean, IsOptional, IsArray, IsUUID, Min, Max<PERSON>ength, ArrayMinSize, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { PlanType, SubscriptionType } from '../entities/plan.entity';
import { PlanFeatureResponseDto } from './plan-features.dto';
import { FeatureType } from '../entities/plan-feature.entity';
import { LegacyFeature } from '../interfaces/legacy-feature.interface';
import { SimplifiedPlanDto } from './simplified-plan.dto';

export class LegacyFeatureDto implements LegacyFeature {
  @ApiProperty({ enum: FeatureType, example: FeatureType.HEC_USER_DIARY })
  @IsEnum(FeatureType)
  @IsNotEmpty()
  type: FeatureType;

  @ApiProperty({ example: 'Hec User Diary' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ example: 'Access to the HEC User Diary platform' })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ example: true })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;
}

export class CreatePlanDto {
  @ApiProperty({ example: 'Premium Plan', description: 'The name of the plan' })
  @IsString()
  @IsNotEmpty({ message: 'Name is required' })
  @MaxLength(100, { message: 'Name cannot be longer than 100 characters' })
  name: string;

  @ApiProperty({ example: 'pro', description: 'The type of the plan', enum: PlanType })
  @IsEnum(PlanType)
  @IsNotEmpty({ message: 'Plan type is required' })
  type: PlanType;

  @ApiProperty({ example: 'monthly', description: 'The subscription type (monthly or yearly)', enum: SubscriptionType })
  @IsEnum(SubscriptionType)
  @IsNotEmpty({ message: 'Subscription type is required' })
  subscriptionType: SubscriptionType;

  @ApiProperty({ example: 'Access to all premium features', description: 'Description of the plan' })
  @IsString()
  @IsNotEmpty({ message: 'Description is required' })
  description: string;

  @ApiProperty({ example: 99.99, description: 'The price of the plan' })
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Price must be a number with at most 2 decimal places' })
  @Min(0, { message: 'Price must be a positive number' })
  @IsNotEmpty({ message: 'Price is required' })
  price: number;

  @ApiProperty({ example: 30, description: 'Duration of the plan in days' })
  @IsNumber({}, { message: 'Duration must be a number' })
  @Min(1, { message: 'Duration must be at least 1 day' })
  @IsNotEmpty({ message: 'Duration is required' })
  durationDays: number;

  @ApiProperty({ example: false, description: 'Whether the plan auto-renews by default' })
  @IsBoolean()
  @IsOptional()
  autoRenew?: boolean = false;

  @ApiProperty({
    example: [
      {
        type: FeatureType.HEC_USER_DIARY,
        name: 'Hec User Diary',
        description: 'Access to the HEC User Diary platform',
        isActive: true
      },
      {
        type: FeatureType.HEC_PLAY,
        name: 'HEC Play',
        description: 'Access to the HEC Play platform',
        isActive: true
      }
    ],
    description: 'Legacy features included in the plan with activation status',
    type: 'array'
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LegacyFeatureDto)
  @IsOptional()
  legacyFeatures?: LegacyFeature[];

  @ApiProperty({
    example: ['123e4567-e89b-12d3-a456-************', '223e4567-e89b-12d3-a456-************'],
    description: 'Array of feature IDs to include in the plan',
    type: [String]
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one feature ID is required' })
  @IsUUID('4', { each: true, message: 'Each feature ID must be a valid UUID' })
  @IsOptional()
  featureIds?: string[];

  @ApiProperty({ example: true, description: 'Whether the plan is active' })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean = true;
}

export class UpdatePlanDto {
  @ApiProperty({ example: 'Premium Plan', description: 'The name of the plan', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(100, { message: 'Name cannot be longer than 100 characters' })
  name?: string;

  @ApiProperty({ example: 'pro', description: 'The type of the plan', enum: PlanType, required: false })
  @IsEnum(PlanType)
  @IsOptional()
  type?: PlanType;

  @ApiProperty({ example: 'monthly', description: 'The subscription type (monthly or yearly)', enum: SubscriptionType, required: false })
  @IsEnum(SubscriptionType)
  @IsOptional()
  subscriptionType?: SubscriptionType;

  @ApiProperty({ example: 'Access to all premium features', description: 'Description of the plan', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ example: 99.99, description: 'The price of the plan', required: false })
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Price must be a number with at most 2 decimal places' })
  @Min(0, { message: 'Price must be a positive number' })
  @IsOptional()
  price?: number;

  @ApiProperty({ example: 30, description: 'Duration of the plan in days', required: false })
  @IsNumber({}, { message: 'Duration must be a number' })
  @Min(1, { message: 'Duration must be at least 1 day' })
  @IsOptional()
  durationDays?: number;

  @ApiProperty({ example: false, description: 'Whether the plan auto-renews by default', required: false })
  @IsBoolean()
  @IsOptional()
  autoRenew?: boolean;

  @ApiProperty({
    example: [
      {
        type: FeatureType.HEC_USER_DIARY,
        name: 'Hec User Diary',
        description: 'Access to the HEC User Diary platform',
        isActive: true
      },
      {
        type: FeatureType.HEC_PLAY,
        name: 'HEC Play',
        description: 'Access to the HEC Play platform',
        isActive: true
      }
    ],
    description: 'Legacy features included in the plan with activation status',
    type: 'array',
    required: false
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LegacyFeatureDto)
  @IsOptional()
  legacyFeatures?: LegacyFeature[];

  @ApiProperty({
    example: ['123e4567-e89b-12d3-a456-************', '223e4567-e89b-12d3-a456-************'],
    description: 'Array of feature IDs to include in the plan',
    type: [String],
    required: false
  })
  @IsArray()
  @IsUUID('4', { each: true, message: 'Each feature ID must be a valid UUID' })
  @IsOptional()
  featureIds?: string[];

  @ApiProperty({ example: true, description: 'Whether the plan is active', required: false })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}

export class SubscribeToPlanDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'The ID of the plan' })
  @IsString()
  @IsNotEmpty({ message: 'Plan ID is required' })
  planId: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'The ID of the user', required: false })
  @IsString()
  @IsOptional()
  userId?: string;

  @ApiProperty({ example: 'payment_ref_123', description: 'Payment reference', required: false })
  @IsString()
  @IsOptional()
  paymentReference?: string;

  @ApiProperty({ example: true, description: 'Whether to enable auto-renewal', required: false })
  @IsBoolean()
  @IsOptional()
  autoRenew?: boolean;

  @ApiProperty({
    example: 'kcp_card',
    description: 'Payment method for subscription',
    required: false
  })
  @IsOptional()
  @IsString()
  paymentMethod?: string;

  @ApiProperty({
    example: 'https://example.com/payment/success',
    description: 'Return URL after successful payment',
    required: false
  })
  @IsOptional()
  @IsString()
  returnUrl?: string;

  @ApiProperty({
    example: 'https://example.com/payment/cancel',
    description: 'Return URL after cancelled payment',
    required: false
  })
  @IsOptional()
  @IsString()
  cancelUrl?: string;
}

export class PlanResponseDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'The ID of the plan' })
  id: string;

  @ApiProperty({ example: 'Premium Plan', description: 'The name of the plan' })
  name: string;

  @ApiProperty({ example: 'pro', description: 'The type of the plan', enum: PlanType })
  type: PlanType;

  @ApiProperty({ example: 'monthly', description: 'The subscription type (monthly or yearly)', enum: SubscriptionType })
  subscriptionType: SubscriptionType;

  @ApiProperty({ example: false, description: 'Whether the plan auto-renews by default' })
  autoRenew: boolean;

  @ApiProperty({ example: 'Access to all premium features', description: 'Description of the plan' })
  description: string;

  @ApiProperty({ example: 99.99, description: 'The price of the plan' })
  price: number;

  @ApiProperty({ example: 30, description: 'Duration of the plan in days' })
  durationDays: number;

  @ApiProperty({
    example: [
      {
        type: FeatureType.HEC_USER_DIARY,
        name: 'Hec User Diary',
        description: 'Access to the HEC User Diary platform',
        isActive: true
      },
      {
        type: FeatureType.HEC_PLAY,
        name: 'HEC Play',
        description: 'Access to the HEC Play platform',
        isActive: true
      }
    ],
    description: 'Legacy features included in the plan with activation status',
    type: 'array'
  })
  legacyFeatures: LegacyFeature[];

  @ApiProperty({
    type: [PlanFeatureResponseDto],
    description: 'Features included in the plan'
  })
  planFeatures: PlanFeatureResponseDto[];

  @ApiProperty({ example: true, description: 'Whether the plan is active' })
  isActive: boolean;

  @ApiProperty({ example: true, description: 'Whether the plan is applicable for promotion', required: false })
  isApplicableForPromotion?: boolean;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'The ID of the promotion applied to this plan', required: false })
  promotionId?: string;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z', description: 'When the plan was created' })
  createdAt: Date;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z', description: 'When the plan was last updated' })
  updatedAt: Date;
}

export class UserPlanResponseDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'The ID of the user plan' })
  id: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'The ID of the user' })
  userId: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'The ID of the plan' })
  planId: string;

  @ApiProperty({ example: 'Premium Plan', description: 'The name of the plan' })
  planName: string;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z', description: 'When the subscription starts' })
  startDate: Date;

  @ApiProperty({ example: '2023-02-01T00:00:00.000Z', description: 'When the subscription ends' })
  endDate: Date;

  @ApiProperty({ example: true, description: 'Whether the subscription is active' })
  isActive: boolean;

  @ApiProperty({ example: 'payment_ref_123', description: 'Payment reference' })
  paymentReference: string;

  @ApiProperty({ example: true, description: 'Whether the subscription is paid' })
  isPaid: boolean;

  @ApiProperty({ example: true, description: 'Whether the subscription auto-renews' })
  autoRenew: boolean;

  @ApiProperty({ example: '2023-01-15T00:00:00.000Z', description: 'When the subscription was last renewed', required: false })
  lastRenewalDate?: Date;

  @ApiProperty({ example: '2023-02-15T00:00:00.000Z', description: 'When the subscription will next renew', required: false })
  nextRenewalDate?: Date;

  @ApiProperty({ example: null, description: 'When the subscription was cancelled', required: false })
  cancellationDate?: Date;

  @ApiProperty({ example: 'Cancelled due to upgrade to Premium plan', description: 'Notes about the subscription', required: false })
  notes?: string;

  @ApiProperty({
    type: SimplifiedPlanDto,
    description: 'Detailed information about the plan',
    required: false
  })
  plan?: SimplifiedPlanDto;

  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'New JWT token with updated plan information',
    required: false
  })
  access_token?: string;

  @ApiProperty({
    required: false,
    description: 'Payment transaction ID if payment gateway was used'
  })
  paymentTransactionId?: string;

  @ApiProperty({
    required: false,
    description: 'Payment URL for gateway payments'
  })
  paymentUrl?: string;
}
