import { <PERSON>, <PERSON>, Param, <PERSON>s, NotFoundException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import * as fs from 'fs';
import * as path from 'path';

import { Public } from '../../common/decorators/public-api.decorator';
import LoggerService from '../../common/services/logger.service';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { StorageProvider } from '../../common/enums/storage.enum';

@ApiTags('Media')
@Controller('media')
export class MediaController {
  constructor(
    private readonly logger: LoggerService,
    private readonly fileRegistryService: FileRegistryService,
  ) {}

  @Get('profile-pictures/:userId')
  @Public()
  @ApiOperation({
    summary: 'Serve a profile picture',
    description: 'Serves a profile picture for a specific user.',
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'The profile picture file',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Profile picture not found',
  })
  async serveProfilePicture(@Param('userId') userId: string, @Res() res: Response): Promise<void> {
    try {
      this.logger.log(`Serving profile picture for user ID: ${userId}`);

      // First try to get the registry entry directly by ID
      let profilePicture = await this.fileRegistryService.getFileByRegistryId(FileEntityType.PROFILE_PICTURE, userId);

      // If not found by registry ID, try to get by user ID
      if (!profilePicture) {
        this.logger.log(`Registry not found by ID, trying to get by user ID: ${userId}`);
        profilePicture = await this.fileRegistryService.getFile(FileEntityType.PROFILE_PICTURE, userId);
      }

      // If still not found, try to get the profile picture directly
      if (!profilePicture) {
        this.logger.warn(`Profile picture not found for user: ${userId}`);
        throw new NotFoundException('Profile picture not found');
      }

      // Check if this is an S3 file
      if (profilePicture.storageProvider === StorageProvider.S3 && profilePicture.storageKey) {
        this.logger.log(`Serving S3 file for profile picture: ${userId}`);

        // For S3 files, get the file stream and serve it directly
        try {
          const fileStream = await this.fileRegistryService.getS3FileStream(profilePicture.storageKey);
          const contentType = profilePicture.mimeType || 'image/jpeg';

          this.logger.log(`Serving S3 file with content type: ${contentType}`);

          // Set response headers
          res.setHeader('Content-Type', contentType);
          res.setHeader('Cache-Control', 'max-age=300'); // Cache for 5 minutes
          res.setHeader('Content-Disposition', 'inline');

          // Pipe the S3 stream directly to the response
          return fileStream.pipe(res);
        } catch (s3Error) {
          this.logger.error(`Error streaming S3 file: ${s3Error.message}`);
          throw new NotFoundException('Profile picture file not found in S3');
        }
      }

      // Handle local files
      const filePath = profilePicture.filePath || profilePicture.FilePath;
      if (!filePath) {
        this.logger.warn(`Profile picture file path not found in registry: ${userId}`);
        throw new NotFoundException('Profile picture file path not found');
      }

      // Normalize the file path to handle both Windows and Linux path separators
      let normalizedFilePath = filePath;
      // Replace Windows backslashes with forward slashes if present
      if (normalizedFilePath.includes('\\')) {
        normalizedFilePath = normalizedFilePath.replace(/\\/g, '/');
      }

      // Get the absolute file path - don't include process.cwd() since UPLOAD_DIR is already absolute in Docker
      const uploadDir = process.env.UPLOAD_DIR || path.join(process.cwd(), 'uploads');
      const absoluteFilePath = path.join(uploadDir, normalizedFilePath);
      this.logger.log(`Absolute file path: ${absoluteFilePath}`);

      // Check if the file exists
      if (!fs.existsSync(absoluteFilePath)) {
        this.logger.warn(`Profile picture file not found at path: ${absoluteFilePath}`);
        throw new NotFoundException('Profile picture file not found');
      }

      // Determine the content type based on file extension if not available in registry
      // Handle both camelCase and PascalCase property names
      let contentType = profilePicture.mimeType || profilePicture.MimeType || 'image/jpeg';
      if (!contentType || !contentType.startsWith('image/')) {
        const ext = path.extname(filePath).toLowerCase();
        if (ext === '.jpg' || ext === '.jpeg') {
          contentType = 'image/jpeg';
        } else if (ext === '.png') {
          contentType = 'image/png';
        } else if (ext === '.gif') {
          contentType = 'image/gif';
        } else if (ext === '.webp') {
          contentType = 'image/webp';
        } else {
          // Default to JPEG if we can't determine the type
          contentType = 'image/jpeg';
        }
      }

      this.logger.log(`Serving file with content type: ${contentType}`);

      // Set response headers and send the file
      res.setHeader('Content-Type', contentType);
      res.setHeader('Cache-Control', 'max-age=300'); // Cache for 5 minutes
      res.setHeader('Content-Disposition', 'inline');

      // Send the file directly
      return res.sendFile(absoluteFilePath);
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Re-throw not found exceptions
        throw error;
      }

      // Log other errors and throw a generic not found exception
      this.logger.error(`Error serving profile picture: ${error.message}`);
      throw new NotFoundException('Profile picture not found');
    }
  }

  @Get('shop-items/:itemId')
  @Public()
  @ApiOperation({
    summary: 'Serve a shop item file',
    description: 'Serves a file for a specific shop item.',
  })
  @ApiParam({
    name: 'itemId',
    description: 'Shop item ID',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'The shop item file',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Shop item file not found',
  })
  async serveShopItemFile(@Param('itemId') itemId: string, @Res() res: Response): Promise<void> {
    try {
      this.logger.log(`Serving shop item file for ID: ${itemId}`);

      // First try to get the registry entry directly by ID
      let shopItemRegistry = await this.fileRegistryService.getFileByRegistryId(FileEntityType.SHOP_ITEM, itemId);

      // If not found by registry ID, try to get by shop item ID
      if (!shopItemRegistry) {
        this.logger.log(`Registry not found by ID, trying to get by shop item ID: ${itemId}`);
        shopItemRegistry = await this.fileRegistryService.getFile(FileEntityType.SHOP_ITEM, itemId);
      }

      // If still not found, try to get the shop item directly
      if (!shopItemRegistry) {
        this.logger.warn(`Shop item registry entry not found for ID: ${itemId}`);
        throw new NotFoundException('Shop item not found');
      }

      // Check if this is an S3 file
      if (shopItemRegistry.storageProvider === StorageProvider.S3 && shopItemRegistry.storageKey) {
        this.logger.log(`Serving S3 file for shop item: ${itemId}`);

        // For S3 files, get the file stream and serve it directly
        try {
          const fileStream = await this.fileRegistryService.getS3FileStream(shopItemRegistry.storageKey);
          const contentType = shopItemRegistry.mimeType || 'application/octet-stream';

          this.logger.log(`Serving S3 file with content type: ${contentType}`);

          // Set response headers
          res.setHeader('Content-Type', contentType);
          res.setHeader('Cache-Control', 'max-age=300'); // Cache for 5 minutes
          res.setHeader('Content-Disposition', 'inline');

          // Pipe the S3 stream directly to the response
          return fileStream.pipe(res);
        } catch (s3Error) {
          this.logger.error(`Error streaming S3 file: ${s3Error.message}`);
          throw new NotFoundException('Shop item file not found in S3');
        }
      }

      // Handle local files
      const filePath = shopItemRegistry.filePath || shopItemRegistry.FilePath;
      if (!filePath) {
        this.logger.warn(`Shop item file path not found in registry: ${itemId}`);
        throw new NotFoundException('Shop item file path not found');
      }

      // Normalize the file path to handle both Windows and Linux path separators
      let normalizedFilePath = filePath;
      // Replace Windows backslashes with forward slashes if present
      if (normalizedFilePath.includes('\\')) {
        normalizedFilePath = normalizedFilePath.replace(/\\/g, '/');
      }

      // Get the absolute file path - don't include process.cwd() since UPLOAD_DIR is already absolute in Docker
      const uploadDir = process.env.UPLOAD_DIR || path.join(process.cwd(), 'uploads');
      const absoluteFilePath = path.join(uploadDir, normalizedFilePath);
      this.logger.log(`Absolute file path: ${absoluteFilePath}`);

      // Check if the file exists
      if (!fs.existsSync(absoluteFilePath)) {
        this.logger.warn(`Shop item file not found at path: ${absoluteFilePath}`);
        throw new NotFoundException('Shop item file not found');
      }

      // Determine the content type based on file extension if not available in registry
      // Handle both camelCase and PascalCase property names
      let contentType = shopItemRegistry.mimeType || shopItemRegistry.MimeType || 'image/jpeg';
      if (!contentType || !contentType.startsWith('image/')) {
        const ext = path.extname(filePath).toLowerCase();
        if (ext === '.jpg' || ext === '.jpeg') {
          contentType = 'image/jpeg';
        } else if (ext === '.png') {
          contentType = 'image/png';
        } else if (ext === '.gif') {
          contentType = 'image/gif';
        } else if (ext === '.webp') {
          contentType = 'image/webp';
        } else {
          // Default to JPEG if we can't determine the type
          contentType = 'image/jpeg';
        }
      }

      this.logger.log(`Serving file with content type: ${contentType}`);

      // Set response headers and send the file
      res.setHeader('Content-Type', contentType);
      res.setHeader('Cache-Control', 'max-age=300'); // Cache for 5 minutes
      res.setHeader('Content-Disposition', 'inline');

      // Send the file directly
      return res.sendFile(absoluteFilePath);
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Re-throw not found exceptions
        throw error;
      }

      // Log other errors and throw a generic not found exception
      this.logger.error(`Error serving shop item file: ${error.message}`);
      throw new NotFoundException('Shop item file not found');
    }
  }

  @Get('diary-skins/:skinId')
  @Public()
  @ApiOperation({
    summary: 'Serve a diary skin file',
    description: 'Serves a file for a specific diary skin.',
  })
  @ApiParam({
    name: 'skinId',
    description: 'Diary skin ID',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'The diary skin file',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Diary skin file not found',
  })
  async serveDiarySkinFile(@Param('skinId') skinId: string, @Res() res: Response): Promise<void> {
    try {
      this.logger.log(`Serving diary skin file for ID: ${skinId}`);

      // First try to get the registry entry directly by ID
      let diarySkinRegistry = await this.fileRegistryService.getFileByRegistryId(FileEntityType.DIARY_SKIN, skinId);

      // If not found by registry ID, try to get by diary skin ID
      if (!diarySkinRegistry) {
        this.logger.log(`Registry not found by ID, trying to get by diary skin ID: ${skinId}`);
        diarySkinRegistry = await this.fileRegistryService.getFile(FileEntityType.DIARY_SKIN, skinId);
      }

      // If still not found, try to get the diary skin directly
      if (!diarySkinRegistry) {
        this.logger.warn(`Diary skin registry entry not found for ID: ${skinId}`);
        throw new NotFoundException('Diary skin not found');
      }

      // Check if this is an S3 file
      if (diarySkinRegistry.storageProvider === StorageProvider.S3 && diarySkinRegistry.storageKey) {
        this.logger.log(`Serving S3 file for diary skin: ${skinId}`);

        // For S3 files, get the file stream and serve it directly
        try {
          const fileStream = await this.fileRegistryService.getS3FileStream(diarySkinRegistry.storageKey);
          const contentType = diarySkinRegistry.mimeType || 'image/jpeg';

          this.logger.log(`Serving S3 file with content type: ${contentType}`);

          // Set response headers
          res.setHeader('Content-Type', contentType);
          res.setHeader('Cache-Control', 'max-age=300'); // Cache for 5 minutes
          res.setHeader('Content-Disposition', 'inline');

          // Pipe the S3 stream directly to the response
          return fileStream.pipe(res);
        } catch (s3Error) {
          this.logger.error(`Error streaming S3 file: ${s3Error.message}`);
          throw new NotFoundException('Diary skin file not found in S3');
        }
      }

      // Handle local files
      const filePath = diarySkinRegistry.filePath || diarySkinRegistry.FilePath;
      if (!filePath) {
        this.logger.warn(`Diary skin file path not found in registry: ${skinId}`);
        throw new NotFoundException('Diary skin file path not found');
      }

      // Normalize the file path to handle both Windows and Linux path separators
      let normalizedFilePath = filePath;
      // Replace Windows backslashes with forward slashes if present
      if (normalizedFilePath.includes('\\')) {
        normalizedFilePath = normalizedFilePath.replace(/\\/g, '/');
      }

      // Get the absolute file path - don't include process.cwd() since UPLOAD_DIR is already absolute in Docker
      const uploadDir = process.env.UPLOAD_DIR || path.join(process.cwd(), 'uploads');
      const absoluteFilePath = path.join(uploadDir, normalizedFilePath);
      this.logger.log(`Absolute file path: ${absoluteFilePath}`);

      // Check if the file exists
      if (!fs.existsSync(absoluteFilePath)) {
        this.logger.warn(`Diary skin file not found at path: ${absoluteFilePath}`);
        throw new NotFoundException('Diary skin file not found');
      }

      // Determine the content type based on file extension if not available in registry
      // Handle both camelCase and PascalCase property names
      let contentType = diarySkinRegistry.mimeType || diarySkinRegistry.MimeType || 'image/jpeg';
      if (!contentType || !contentType.startsWith('image/')) {
        const ext = path.extname(filePath).toLowerCase();
        if (ext === '.jpg' || ext === '.jpeg') {
          contentType = 'image/jpeg';
        } else if (ext === '.png') {
          contentType = 'image/png';
        } else if (ext === '.gif') {
          contentType = 'image/gif';
        } else if (ext === '.webp') {
          contentType = 'image/webp';
        } else {
          // Default to JPEG if we can't determine the type
          contentType = 'image/jpeg';
        }
      }

      this.logger.log(`Serving file with content type: ${contentType}`);

      // Set response headers and send the file
      res.setHeader('Content-Type', contentType);
      res.setHeader('Cache-Control', 'max-age=300'); // Cache for 5 minutes
      res.setHeader('Content-Disposition', 'inline');

      // Send the file directly without root option
      return res.sendFile(absoluteFilePath);
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Re-throw not found exceptions
        throw error;
      }

      // Log other errors with more details and throw a generic not found exception
      this.logger.error(`Error serving diary skin file: ${error.message}`);
      if (error.stack) {
        this.logger.error(`Stack trace: ${error.stack}`);
      }
      throw new NotFoundException('Diary skin file not found');
    }
  }

  @Get('diary-qr/:entryId')
  @Public()
  @ApiOperation({
    summary: 'Serve a diary QR code',
    description: 'Serves a QR code for a specific diary entry.',
  })
  @ApiParam({
    name: 'entryId',
    description: 'Diary entry ID',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'The diary QR code file',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Diary QR code not found',
  })
  async serveDiaryQrFile(@Param('entryId') entryId: string, @Res() res: Response): Promise<void> {
    try {
      this.logger.log(`Serving diary QR code for entry ID: ${entryId}`);

      // First try to get the registry entry directly by ID
      let diaryQrRegistry = await this.fileRegistryService.getFileByRegistryId(FileEntityType.DIARY_QR, entryId);

      // If not found by registry ID, try to get by diary entry ID
      if (!diaryQrRegistry) {
        this.logger.log(`Registry not found by ID, trying to get by diary entry ID: ${entryId}`);
        diaryQrRegistry = await this.fileRegistryService.getFile(FileEntityType.DIARY_QR, entryId);
      }

      // If still not found, throw an exception
      if (!diaryQrRegistry) {
        this.logger.warn(`Diary QR registry entry not found for entry ID: ${entryId}`);
        throw new NotFoundException('Diary QR code not found');
      }

      // Check if this is an S3 file
      if (diaryQrRegistry.storageProvider === StorageProvider.S3 && diaryQrRegistry.storageKey) {
        this.logger.log(`Serving S3 file for diary QR: ${entryId}`);

        // For S3 files, get the file stream and serve it directly
        try {
          const fileStream = await this.fileRegistryService.getS3FileStream(diaryQrRegistry.storageKey);
          const contentType = diaryQrRegistry.mimeType || 'image/png';

          this.logger.log(`Serving S3 file with content type: ${contentType}`);

          // Set response headers
          res.setHeader('Content-Type', contentType);
          res.setHeader('Cache-Control', 'max-age=300'); // Cache for 5 minutes
          res.setHeader('Content-Disposition', 'inline');

          // Pipe the S3 stream directly to the response
          return fileStream.pipe(res);
        } catch (s3Error) {
          this.logger.error(`Error streaming S3 file: ${s3Error.message}`);
          throw new NotFoundException('Diary QR file not found in S3');
        }
      }

      // Handle local files
      const filePath = diaryQrRegistry.filePath || diaryQrRegistry.FilePath;
      if (!filePath) {
        this.logger.warn(`Diary QR file path not found in registry: ${entryId}`);
        throw new NotFoundException('Diary QR file path not found');
      }

      // Normalize the file path to handle both Windows and Linux path separators
      let normalizedFilePath = filePath;
      // Replace Windows backslashes with forward slashes if present
      if (normalizedFilePath.includes('\\')) {
        normalizedFilePath = normalizedFilePath.replace(/\\/g, '/');
      }

      // Get the absolute file path - don't include process.cwd() since UPLOAD_DIR is already absolute in Docker
      const uploadDir = process.env.UPLOAD_DIR || path.join(process.cwd(), 'uploads');
      const absoluteFilePath = path.join(uploadDir, normalizedFilePath);
      this.logger.log(`Absolute file path: ${absoluteFilePath}`);

      // Check if the file exists
      if (!fs.existsSync(absoluteFilePath)) {
        this.logger.warn(`Diary QR file not found at path: ${absoluteFilePath}`);
        throw new NotFoundException('Diary QR file not found');
      }      // Determine the content type based on file extension if not available in registry
      let contentType = diaryQrRegistry.mimeType || 'image/png';
      if (!contentType || !contentType.startsWith('image/')) {
        const ext = path.extname(filePath).toLowerCase();
        if (ext === '.jpg' || ext === '.jpeg') {
          contentType = 'image/jpeg';
        } else if (ext === '.png') {
          contentType = 'image/png';
        } else if (ext === '.gif') {
          contentType = 'image/gif';
        } else if (ext === '.webp') {
          contentType = 'image/webp';
        } else {
          // Default to PNG if we can't determine the type
          contentType = 'image/png';
        }
      }

      this.logger.log(`Serving file with content type: ${contentType}`);

      // Set response headers and send the file
      res.setHeader('Content-Type', contentType);
      // Prevent caching to ensure latest QR code is always served
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('Content-Disposition', 'inline');

      // Send the file directly
      return res.sendFile(absoluteFilePath);
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Re-throw not found exceptions
        throw error;
      }

      // Log other errors and throw a generic not found exception
      this.logger.error(`Error serving diary QR file: ${error.message}`);
      throw new NotFoundException('Diary QR file not found');
    }
  }

  @Get('story-maker/:storyId')
  @Public()
  @ApiOperation({
    summary: 'Serve a story maker picture',
    description: 'Serves a picture for a specific story maker story.',
  })
  @ApiParam({
    name: 'storyId',
    description: 'Story ID',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'The story maker picture file',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Story maker picture not found',
  })
  async serveStoryMakerPicture(@Param('storyId') storyId: string, @Res() res: Response): Promise<void> {
    try {
      // First try to get the registry entry directly by ID
      let storyMakerRegistry = await this.fileRegistryService.getFileByRegistryId(FileEntityType.STORY_MAKER, storyId);

      // If not found by registry ID, try to get by story maker ID
      if (!storyMakerRegistry) {
        storyMakerRegistry = await this.fileRegistryService.getFile(FileEntityType.STORY_MAKER, storyId);
      }

      // If still not found, throw an exception
      if (!storyMakerRegistry) {
        this.logger.warn(`Story maker registry entry not found for ID: ${storyId}`);
        throw new NotFoundException('Story maker picture not found');
      }

      // Check if this is an S3 file
      if (storyMakerRegistry.storageProvider === StorageProvider.S3 && storyMakerRegistry.storageKey) {
        this.logger.log(`Serving S3 file for story maker: ${storyId}`);

        // For S3 files, get the file stream and serve it directly
        try {
          const fileStream = await this.fileRegistryService.getS3FileStream(storyMakerRegistry.storageKey);
          const contentType = storyMakerRegistry.mimeType || 'image/jpeg';

          this.logger.log(`Serving S3 file with content type: ${contentType}`);

          // Set response headers
          res.setHeader('Content-Type', contentType);
          res.setHeader('Cache-Control', 'max-age=300'); // Cache for 5 minutes
          res.setHeader('Content-Disposition', 'inline');

          // Pipe the S3 stream directly to the response
          return fileStream.pipe(res);
        } catch (s3Error) {
          this.logger.error(`Error streaming S3 file: ${s3Error.message}`);
          throw new NotFoundException('Story maker file not found in S3');
        }
      }

      // Handle local files
      const filePath = storyMakerRegistry.filePath || storyMakerRegistry.FilePath;
      if (!filePath) {
        this.logger.warn(`Story maker file path not found in registry: ${storyId}`);
        throw new NotFoundException('Story maker picture file path not found');
      }

      // Normalize the file path to handle both Windows and Linux path separators
      let normalizedFilePath = filePath;
      if (normalizedFilePath.includes('\\')) {
        normalizedFilePath = normalizedFilePath.replace(/\\/g, '/');
      }

      // Get the upload directory
      const uploadDir = process.env.UPLOAD_DIR || path.join(process.cwd(), 'uploads');

      // For Windows compatibility, ensure we're using a consistent path format
      const normalizedUploadDir = uploadDir.replace(/\\/g, '/');

      // Create the absolute file path
      const absoluteFilePath = path.join(normalizedUploadDir, normalizedFilePath);

      // Also create a root directory and relative path for Express sendFile
      const rootDir = process.cwd();
      const relativeToRootPath = path.relative(rootDir, absoluteFilePath);

      // Check if the file exists
      if (!fs.existsSync(absoluteFilePath)) {
        this.logger.warn(`Story maker picture file not found at path: ${absoluteFilePath}`);
        throw new NotFoundException('Story maker picture file not found');
      }

      // Determine the content type
      let contentType = storyMakerRegistry.mimeType || 'image/jpeg';
      if (!contentType || !contentType.startsWith('image/')) {
        const ext = path.extname(filePath).toLowerCase();
        if (ext === '.jpg' || ext === '.jpeg') {
          contentType = 'image/jpeg';
        } else if (ext === '.png') {
          contentType = 'image/png';
        } else if (ext === '.gif') {
          contentType = 'image/gif';
        } else {
          contentType = 'image/jpeg';
        }
      }

      res.setHeader('Content-Type', contentType);
      res.setHeader('Cache-Control', 'max-age=300'); // Cache for 5 minutes
      res.setHeader('Content-Disposition', 'inline');

      // Send the file using the root directory and relative path
      return res.sendFile(relativeToRootPath, { root: rootDir });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error serving story maker picture: ${error.message}`);
      throw new NotFoundException('Story maker picture not found');
    }
  }

  @Get('message-attachments/:id')
  @Public()
  @ApiOperation({
    summary: 'Serve a message attachment file',
    description: 'Serves a file attachment for a specific message.',
  })
  @ApiParam({
    name: 'id',
    description: 'Message attachment registry ID',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'The message attachment file',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Message attachment not found',
  })
  async serveMessageAttachment(@Param('id') id: string, @Res() res: Response): Promise<void> {
    try {
      this.logger.log(`Serving message attachment for ID: ${id}`);

      // Get the registry entry by ID
      const messageAttachment = await this.fileRegistryService.getFileByRegistryId(FileEntityType.MESSAGE_ATTACHMENT, id);

      if (!messageAttachment) {
        this.logger.warn(`Message attachment registry entry not found for ID: ${id}`);
        throw new NotFoundException('Message attachment not found');
      }

      // Check if this is an S3 file
      if (messageAttachment.storageProvider === StorageProvider.S3 && messageAttachment.storageKey) {
        this.logger.log(`Serving S3 file for message attachment: ${id}`);

        // For S3 files, get the file stream and serve it directly
        try {
          const fileStream = await this.fileRegistryService.getS3FileStream(messageAttachment.storageKey);
          const contentType = messageAttachment.mimeType || 'application/octet-stream';

          this.logger.log(`Serving S3 file with content type: ${contentType}`);

          // Set response headers
          res.setHeader('Content-Type', contentType);
          res.setHeader('Cache-Control', 'max-age=300'); // Cache for 5 minutes
          res.setHeader('Content-Disposition', 'inline');

          // Pipe the S3 stream directly to the response
          return fileStream.pipe(res);
        } catch (s3Error) {
          this.logger.error(`Error streaming S3 file: ${s3Error.message}`);
          throw new NotFoundException('Message attachment file not found in S3');
        }
      }

      // Handle local files
      const filePath = messageAttachment.filePath || messageAttachment.FilePath;
      if (!filePath) {
        this.logger.warn(`Message attachment file path not found in registry: ${id}`);
        throw new NotFoundException('Message attachment file path not found');
      }

      // Normalize the file path to handle both Windows and Linux path separators
      let normalizedFilePath = filePath;
      if (normalizedFilePath.includes('\\')) {
        normalizedFilePath = normalizedFilePath.replace(/\\/g, '/');
      }

      // Get the absolute file path
      const uploadDir = process.env.UPLOAD_DIR || path.join(process.cwd(), 'uploads');
      const absoluteFilePath = path.join(uploadDir, normalizedFilePath);
      this.logger.log(`Absolute file path: ${absoluteFilePath}`);

      // Check if the file exists
      if (!fs.existsSync(absoluteFilePath)) {
        this.logger.warn(`Message attachment file not found at path: ${absoluteFilePath}`);
        throw new NotFoundException('Message attachment file not found');
      }

      // Determine the content type based on file extension if not available in registry
      let contentType = messageAttachment.mimeType || messageAttachment.MimeType || 'application/octet-stream';
      if (!contentType) {
        const ext = path.extname(filePath).toLowerCase();
        if (ext === '.jpg' || ext === '.jpeg') {
          contentType = 'image/jpeg';
        } else if (ext === '.png') {
          contentType = 'image/png';
        } else if (ext === '.gif') {
          contentType = 'image/gif';
        } else if (ext === '.webp') {
          contentType = 'image/webp';
        } else if (ext === '.pdf') {
          contentType = 'application/pdf';
        } else if (ext === '.doc') {
          contentType = 'application/msword';
        } else if (ext === '.docx') {
          contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        } else if (ext === '.txt') {
          contentType = 'text/plain';
        } else if (ext === '.zip') {
          contentType = 'application/zip';
        } else {
          contentType = 'application/octet-stream';
        }
      }

      this.logger.log(`Serving file with content type: ${contentType}`);

      // Get the original filename for download
      const fileName = messageAttachment.fileName || messageAttachment.FileName || `attachment${path.extname(filePath)}`;

      // Set response headers
      res.setHeader('Content-Type', contentType);
      res.setHeader('Cache-Control', 'max-age=300'); // Cache for 5 minutes

      // For images, display inline; for other files, suggest download
      if (contentType.startsWith('image/')) {
        res.setHeader('Content-Disposition', 'inline');
      } else {
        res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileName)}"`);
      }

      // Send the file directly
      return res.sendFile(absoluteFilePath);
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Re-throw not found exceptions
        throw error;
      }

      // Log other errors and throw a generic not found exception
      this.logger.error(`Error serving message attachment: ${error.message}`);
      throw new NotFoundException('Message attachment not found');
    }
  }
}
