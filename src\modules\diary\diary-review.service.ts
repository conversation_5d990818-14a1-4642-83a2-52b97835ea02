import { Injectable, NotFoundException, ConflictException, BadRequestException, ForbiddenException, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In, Not } from 'typeorm';
import { DiaryEntry, DiaryEntryStatus } from '../../database/entities/diary-entry.entity';
import { DiaryFeedback } from '../../database/entities/diary-feedback.entity';
import { DiaryCorrection } from '../../database/entities/diary-correction.entity';
import { User } from '../../database/entities/user.entity';
import { PlanFeature, FeatureType } from '../../database/entities/plan-feature.entity';
import { CreateDiaryFeedbackDto, DiaryFeedbackResponseDto } from '../../database/models/diary.dto';
import { CreateDiaryCorrectionDto, DiaryCorrectionResponseDto } from '../../database/models/diary-correction.dto';
import { FeedbackOnlyDto } from '../../database/models/feedback-only.dto';
import { TutorMatchingService } from '../tutor-matching/tutor-matching.service';
import { NotificationHelperService } from '../notification/notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { getCurrentUTCDate } from '../../common/utils/date-utils';
import { PendingReviewEntryDto } from '../../database/models/diary.dto';
import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';

@Injectable()
export class DiaryReviewService {
  private readonly logger = new Logger(DiaryReviewService.name);
  constructor(
    @InjectRepository(DiaryEntry)
    private readonly diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(DiaryFeedback)
    private readonly diaryFeedbackRepository: Repository<DiaryFeedback>,
    @InjectRepository(DiaryCorrection)
    private readonly diaryCorrectionRepository: Repository<DiaryCorrection>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(PlanFeature)
    private readonly planFeatureRepository: Repository<PlanFeature>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    private readonly dataSource: DataSource,
    @Inject(forwardRef(() => TutorMatchingService))
    private readonly tutorMatchingService: TutorMatchingService,
    private readonly notificationHelper: NotificationHelperService,
    private readonly deeplinkService: DeeplinkService
  ) {}

  /**
   * Get the plan feature ID for the diary module
   * @returns The plan feature ID for the diary module
   */
  private async getDiaryModuleFeatureId(): Promise<string> {
    try {
      // Find the plan feature for HEC_USER_DIARY
      const diaryFeature = await this.planFeatureRepository.findOne({
        where: { type: FeatureType.HEC_USER_DIARY }
      });

      if (!diaryFeature) {
        this.logger.error('Diary module feature (HEC_USER_DIARY) not found in the database');
        throw new NotFoundException('Diary module feature not found');
      }

      this.logger.log(`Found diary module feature with ID: ${diaryFeature.id}`);
      return diaryFeature.id;
    } catch (error) {
      this.logger.error(`Error getting diary module feature ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Submit feedback only for a diary entry
   * @param entryId The ID of the diary entry
   * @param tutorId The ID of the tutor submitting the feedback
   * @param feedbackOnlyDto The feedback text only
   * @returns The created feedback
   */
  async submitFeedbackOnly(
    entryId: string,
    tutorId: string,
    feedbackOnlyDto: FeedbackOnlyDto
  ): Promise<DiaryFeedbackResponseDto> {
    try {
      // Check if the user is a tutor
      const user = await this.userRepository.findOne({
        where: { id: tutorId },
        relations: ['userRoles', 'userRoles.role'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${tutorId} not found`);
      }

      // Check if the user has the tutor role
      const isTutor = user.userRoles.some(userRole => userRole.role.name === 'tutor');
      if (!isTutor) {
        throw new ForbiddenException('Only tutors can provide feedback on diary entries');
      }

      // Get the diary entry
      const entry = await this.diaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['diary', 'diary.user'],
      });

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
      }

      // Check if the entry is in the correct state
      if (entry.status === DiaryEntryStatus.NEW) {
        throw new BadRequestException('Cannot provide feedback on a diary entry that has not been submitted');
      }

      // Create feedback with default values for other fields
      const feedback = this.diaryFeedbackRepository.create({
        diaryEntryId: entryId,
        tutorId: tutorId,
        feedback: feedbackOnlyDto.feedback,
        rating: 3, // Default middle rating
        award: null, // No award by default
      });

      const savedFeedback = await this.diaryFeedbackRepository.save(feedback);

      // Get tutor details
      const tutor = await this.userRepository.findOne({
        where: { id: tutorId },
      });

      // Update last activity date in tutor matching
      if (entry.diary?.user) {
        try {
          // Get the diary module feature ID
          const diaryModuleId = await this.getDiaryModuleFeatureId();

          // Update last activity date using the correct module ID
          await this.tutorMatchingService.updateLastActivityDate(
            entry.diary.userId,
            diaryModuleId
          );

          this.logger.log(`Updated last activity date for student ${entry.diary.userId} and diary module ${diaryModuleId}`);
        } catch (error) {
          this.logger.warn(`Failed to update last activity date: ${error.message}`);
        }
      }

      // Send notification to student
      try {
        // Create HTML content for email notification
        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="color: #333;">New Feedback on Your Diary Entry</h2>
            </div>
            <div style="margin-bottom: 20px;">
              <p>Hello ${entry.diary.user?.name || 'there'},</p>
              <p>${tutor.name} has provided feedback on your diary entry.</p>
              <p>Feedback: "${feedbackOnlyDto.feedback}"</p>
              <p>Please log in to the system to view the complete feedback.</p>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
        `;

        await this.notificationHelper.notify(
          entry.diary.userId,
          NotificationType.DIARY_FEEDBACK,
          'New Feedback on Your Diary Entry',
          `${tutor.name} has provided feedback on your diary entry.`,
          {
            relatedEntityId: entryId,
            relatedEntityType: 'diary_entry',
            htmlContent: htmlContent,
            // Use channel control parameters instead of channels array
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: false
          }
        );
      } catch (error) {
        this.logger.warn(`Failed to send notification: ${error.message}`);
      }

      return {
        id: savedFeedback.id,
        tutorId: savedFeedback.tutorId,
        tutorName: tutor.name,
        feedback: savedFeedback.feedback,
        rating: savedFeedback.rating,
        award: savedFeedback.award,
        createdAt: savedFeedback.createdAt,
      };
    } catch (error) {
      this.logger.error(`Error submitting feedback: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Submit feedback for a diary entry
   * @param entryId The ID of the diary entry
   * @param tutorId The ID of the tutor submitting the feedback
   * @param createDiaryFeedbackDto The feedback data
   * @returns The created feedback
   */
  async submitFeedback(
    entryId: string,
    tutorId: string,
    createDiaryFeedbackDto: CreateDiaryFeedbackDto
  ): Promise<DiaryFeedbackResponseDto> {
    try {
      // Check if the user is a tutor
      const user = await this.userRepository.findOne({
        where: { id: tutorId },
        relations: ['userRoles', 'userRoles.role'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${tutorId} not found`);
      }

      // Check if the user has the tutor role
      const isTutor = user.userRoles.some(userRole => userRole.role.name === 'tutor');
      if (!isTutor) {
        throw new ForbiddenException('Only tutors can provide feedback on diary entries');
      }

      // Get the diary entry
      const entry = await this.diaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['diary', 'diary.user'],
      });

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
      }

      // Check if the entry is in the correct state
      if (entry.status === DiaryEntryStatus.NEW) {
        throw new BadRequestException('Cannot provide feedback on a diary entry that has not been submitted');
      }

      // Create feedback
      const feedback = this.diaryFeedbackRepository.create({
        diaryEntryId: entryId,
        tutorId: tutorId,
        feedback: createDiaryFeedbackDto.feedback,
        rating: createDiaryFeedbackDto.rating || 0,
        award: createDiaryFeedbackDto.award,
      });

      const savedFeedback = await this.diaryFeedbackRepository.save(feedback);

      // Get tutor details
      const tutor = await this.userRepository.findOne({
        where: { id: tutorId },
      });

      // Update last activity date in tutor matching
      if (entry.diary?.user) {
        try {
          // Get the diary module feature ID
          const diaryModuleId = await this.getDiaryModuleFeatureId();

          // Update last activity date using the correct module ID
          await this.tutorMatchingService.updateLastActivityDate(
            entry.diary.userId,
            diaryModuleId
          );

          this.logger.log(`Updated last activity date for student ${entry.diary.userId} and diary module ${diaryModuleId}`);
        } catch (error) {
          this.logger.warn(`Failed to update last activity date: ${error.message}`);
        }
      }

      // Send notification to student
      try {
        // Generate a deep link for the student to view the diary entry
        const viewLink = this.deeplinkService.getWebLink(DeeplinkType.DIARY_ENTRY, {
          id: entryId
        });

        // Create HTML content with the view link
        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="color: #333;">New Feedback on Your Diary Entry</h2>
            </div>
            <div style="margin-bottom: 20px;">
              <p>Hello ${entry.diary.user?.name || 'there'},</p>
              <p>${tutor.name} has provided feedback on your diary entry.</p>
              <p>Feedback: "${createDiaryFeedbackDto.feedback}"</p>
              <p>Click the button below to view the complete feedback.</p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${viewLink}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Feedback</a>
              </div>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
        `;

        await this.notificationHelper.notify(
          entry.diary.userId,
          NotificationType.DIARY_FEEDBACK,
          'New Feedback on Your Diary Entry',
          `${tutor.name} has provided feedback on your diary entry.`,
          {
            relatedEntityId: entryId,
            relatedEntityType: 'diary_entry',
            htmlContent: htmlContent,
            // Use channel control parameters instead of channels array
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: false
          }
        );
      } catch (error) {
        this.logger.warn(`Failed to send notification: ${error.message}`);
      }

      return {
        id: savedFeedback.id,
        tutorId: savedFeedback.tutorId,
        tutorName: tutor.name,
        feedback: savedFeedback.feedback,
        rating: savedFeedback.rating,
        award: savedFeedback.award,
        createdAt: savedFeedback.createdAt,
      };
    } catch (error) {
      this.logger.error(`Error submitting feedback: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Submit a correction for a diary entry
   * @param entryId The ID of the diary entry
   * @param tutorId The ID of the tutor submitting the correction
   * @param createDiaryCorrectionDto The correction data
   * @returns The created correction
   */
  async submitCorrection(
    entryId: string,
    tutorId: string,
    createDiaryCorrectionDto: CreateDiaryCorrectionDto
  ): Promise<DiaryCorrectionResponseDto> {
    try {
      // Check if the user is a tutor
      const user = await this.userRepository.findOne({
        where: { id: tutorId },
        relations: ['userRoles', 'userRoles.role'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${tutorId} not found`);
      }

      // Check if the user has the tutor role
      const isTutor = user.userRoles.some(userRole => userRole.role.name === 'tutor');
      if (!isTutor) {
        throw new ForbiddenException('Only tutors can provide corrections on diary entries');
      }

      // Get the diary entry
      const entry = await this.diaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['diary', 'diary.user', 'correction'],
      });

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
      }

      // Check if the entry is in the correct state
      if (entry.status === DiaryEntryStatus.NEW) {
        throw new BadRequestException('Cannot provide correction on a diary entry that has not been submitted');
      }

      // Check if the entry already has a correction
      if (entry.correction) {
        throw new ConflictException('This diary entry already has a correction');
      }

      // Create a transaction to ensure data consistency
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // First update the entry status to REVIEWED
        entry.status = DiaryEntryStatus.REVIEWED;
        entry.evaluatedAt = new Date();
        entry.evaluatedBy = tutorId;
        entry.score = createDiaryCorrectionDto.score;

        // Save the entry first to ensure it exists
        const savedEntry = await queryRunner.manager.save(entry);

        // Now create the correction with the saved entry ID
        const correction = new DiaryCorrection();
        correction.diaryEntryId = savedEntry.id; // Use the saved entry ID
        correction.tutorId = tutorId;
        correction.correctionText = createDiaryCorrectionDto.correctionText;
        correction.score = createDiaryCorrectionDto.score;
        correction.comments = createDiaryCorrectionDto.comments;

        // Save the correction
        const savedCorrection = await queryRunner.manager.save(correction);

        // Commit the transaction
        await queryRunner.commitTransaction();

        // Get tutor details
        const tutor = await this.userRepository.findOne({
          where: { id: tutorId },
        });

        // Update last activity date in tutor matching
        if (entry.diary?.user) {
          try {
            // Get the diary module feature ID
            const diaryModuleId = await this.getDiaryModuleFeatureId();

            // Update last activity date using the correct module ID
            await this.tutorMatchingService.updateLastActivityDate(
              entry.diary.userId,
              diaryModuleId
            );


          } catch (error) {
            this.logger.warn(`Failed to update last activity date: ${error.message}`);
          }
        }

        // Send notification to student
        try {
          // Generate a deep link for the student to view the diary entry
          const viewLink = this.deeplinkService.getWebLink(DeeplinkType.DIARY_ENTRY, {
            id: entryId
          });

          // Create HTML content with the view link
          const htmlContent = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
              <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #333;">Your Diary Entry Has Been Reviewed</h2>
              </div>
              <div style="margin-bottom: 20px;">
                <p>Hello ${entry.diary.user?.name || 'there'},</p>
                <p>${tutor.name} has reviewed your diary entry and provided corrections.</p>
                <p>Score: ${createDiaryCorrectionDto.score}/10</p>
                <p>Click the button below to view the complete review and corrections.</p>
                <div style="text-align: center; margin: 30px 0;">
                  <a href="${viewLink}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Review</a>
                </div>
              </div>
              <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
                <p>This is an automated message from the HEC system.</p>
                <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
              </div>
            </div>
          `;

          await this.notificationHelper.notify(
            entry.diary.userId,
            NotificationType.DIARY_REVIEW,
            'Your Diary Entry Has Been Reviewed',
            `${tutor.name} has reviewed your diary entry and provided corrections.`,
            {
              relatedEntityId: entryId,
              relatedEntityType: 'diary_entry',
              htmlContent: htmlContent,
              // Use channel control parameters instead of channels array
              sendEmail: true,
              sendPush: true,
              sendInApp: true,
              sendMobile: true,
              sendSms: false,
              sendRealtime: false
            }
          );
        } catch (error) {
          this.logger.warn(`Failed to send notification: ${error.message}`);
        }

        return {
          id: savedCorrection.id,
          diaryEntryId: savedCorrection.diaryEntryId,
          tutorId: savedCorrection.tutorId,
          tutorName: tutor.name,
          correctionText: savedCorrection.correctionText,
          score: savedCorrection.score,
          comments: savedCorrection.comments,
          createdAt: savedCorrection.createdAt,
          updatedAt: savedCorrection.updatedAt,
        };
      } catch (error) {
        // Rollback the transaction in case of error
        await queryRunner.rollbackTransaction();
        this.logger.error(`Error submitting correction: ${error.message}`, error.stack);
        throw error;
      } finally {
        // Release the query runner
        await queryRunner.release();
      }
    } catch (error) {
      this.logger.error(`Error submitting correction: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Confirm a diary entry review
   * @param entryId The ID of the diary entry
   * @param tutorId The ID of the tutor confirming the review
   * @returns The updated diary entry
   */
  async confirmDiaryEntryReview(entryId: string, tutorId: string): Promise<DiaryEntry> {
    try {
      // Check if the user is a tutor
      const user = await this.userRepository.findOne({
        where: { id: tutorId },
        relations: ['userRoles', 'userRoles.role'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${tutorId} not found`);
      }

      // Check if the user has the tutor role
      const isTutor = user.userRoles.some(userRole => userRole.role.name === 'tutor');
      if (!isTutor) {
        throw new ForbiddenException('Only tutors can confirm diary entry reviews');
      }

      // Get the diary entry
      const entry = await this.diaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['diary', 'diary.user', 'correction', 'feedbacks'],
      });

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
      }

      // Update the entry status
      entry.status = DiaryEntryStatus.CONFIRM;

      // Save the entry
      return await this.diaryEntryRepository.save(entry);
    } catch (error) {
      this.logger.error(`Error confirming diary entry review: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Add a thanks message to a diary entry
   * @param entryId The ID of the diary entry
   * @param userId The ID of the user adding the thanks message
   * @param thanksMessage The thanks message
   * @returns The updated diary entry
   */
  async addThanksMessage(entryId: string, userId: string, thanksMessage: string): Promise<DiaryEntry> {
    try {
      // Get the diary entry
      const entry = await this.diaryEntryRepository.findOne({
        where: { id: entryId },
        relations: ['diary', 'diary.user'],
      });

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
      }

      // Check if the user owns the diary entry
      if (entry.diary.userId !== userId) {
        throw new ForbiddenException('You do not have permission to add a thanks message to this diary entry');
      }

      // Check if the entry is in the correct state
      if (entry.status !== DiaryEntryStatus.CONFIRM) {
        throw new BadRequestException(`Cannot add thanks message to a diary entry with status ${entry.status}`);
      }

      // Update the entry
      entry.thanksMessage = thanksMessage;

      // Save the entry
      return await this.diaryEntryRepository.save(entry);
    } catch (error) {
      this.logger.error(`Error adding thanks message: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get pending review entries for a tutor
   * @param tutorId ID of the tutor
   * @param paginationDto Pagination options
   * @returns Paginated list of pending review entries
   */
  async getPendingReviewEntries(tutorId?: string, paginationDto?: PaginationDto): Promise<PagedListDto<PendingReviewEntryDto>> {
    try {
      // Get the current date and time in UTC
      const now = getCurrentUTCDate();
      this.logger.log(`[getPendingReviewEntries] Current UTC time: ${now.toISOString()}`);      // Get all active student assignments for this tutor using the repository
      const studentTutorMappings = await this.studentTutorMappingRepository
        .createQueryBuilder('mapping')
        .select('mapping.studentId')
        .where('mapping.tutorId = :tutorId', { tutorId })
        .andWhere('mapping.status = :mappingStatus', { mappingStatus: MappingStatus.ACTIVE })
        .getMany();

      const assignedStudentIds = studentTutorMappings.map(mapping => mapping.studentId);

      // Get total count for pagination (all non-CONFIRM entries from assigned students)
      const totalCount = await this.diaryEntryRepository.count({
        where: {
          diary: {
            userId: In(assignedStudentIds)
          },
          status: Not(DiaryEntryStatus.CONFIRM)
        }
      });

      // Apply pagination if provided
      let options: any = {
        where: {
          diary: {
            userId: In(assignedStudentIds)
          },
          status: Not(DiaryEntryStatus.CONFIRM)
        },
        relations: [
          'diary',
          'diary.user',
          'feedbacks',
          'feedbacks.tutor',
          'settings',
          'settings.settingsTemplate'
        ],
        order: { updatedAt: 'ASC' } // Oldest submissions first
      };

      // Extract filter parameters from paginationDto
      let moduleFilter: string = null;
      let studentNameFilter: string = null;
      let dateFromFilter: Date = null;
      let dateToFilter: Date = null;

      if (paginationDto) {
        const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto;
        const skip = (page - 1) * limit;

        options.skip = skip;
        options.take = limit;

        if (sortBy && sortDirection) {
          options.order = { [sortBy]: sortDirection };
        }

        // Extract custom filter parameters if they exist
        if (paginationDto['moduleTitle']) {
          moduleFilter = paginationDto['moduleTitle'];
        }

        if (paginationDto['studentName']) {
          studentNameFilter = paginationDto['studentName'];
        }

        if (paginationDto['dateFrom']) {
          try {
            dateFromFilter = new Date(paginationDto['dateFrom']);
          } catch (e) {
            this.logger.warn(`Invalid dateFrom format: ${paginationDto['dateFrom']}`);
          }
        }

        if (paginationDto['dateTo']) {
          try {
            dateToFilter = new Date(paginationDto['dateTo']);
          } catch (e) {
            this.logger.warn(`Invalid dateTo format: ${paginationDto['dateTo']}`);
          }
        }
      }

      const allEntries = await this.diaryEntryRepository.find(options);

      this.logger.log(`[getPendingReviewEntries] Found ${allEntries.length} total entries`);

      // Log the status distribution of entries
      const statusCounts = {
        [DiaryEntryStatus.NEW]: 0,
        [DiaryEntryStatus.SUBMIT]: 0,
        [DiaryEntryStatus.REVIEWED]: 0,
        [DiaryEntryStatus.CONFIRM]: 0
      };

      for (const entry of allEntries) {
        statusCounts[entry.status]++;
      }

      this.logger.log('[getPendingReviewEntries] Status distribution:', statusCounts);

      // Reset entries that have expired locks
      let resetCount = 0;
      for (const entry of allEntries) {
        if (entry.status === DiaryEntryStatus.SUBMIT && entry.reviewExpiryTime && entry.reviewExpiryTime < now) {
          this.logger.log(`[getPendingReviewEntries] Resetting expired lock for entry ${entry.id}. Expiry: ${entry.reviewExpiryTime?.toISOString()}, Now: ${now.toISOString()}`);
          entry.status = DiaryEntryStatus.SUBMIT;
          entry.reviewStartTime = null;
          entry.reviewExpiryTime = null;
          entry.reviewingTutorId = null;
          await this.diaryEntryRepository.save(entry);
          resetCount++;
        }
      }

      if (resetCount > 0) {
        this.logger.log(`[getPendingReviewEntries] Reset ${resetCount} entries with expired locks`);
      }

      // Include ALL entries in the response, but mark their status appropriately
      const availableEntries = allEntries.map(entry => ({
        id: entry.id,
        title: entry.title,
        entryDate: entry.entryDate,
        studentName: entry.diary?.user?.name,
        studentId: entry.diary?.userId,
        submittedAt: entry.updatedAt,
        status: entry.status,
        reviewedByCurrentTutor: tutorId && entry.feedbacks?.some(feedback => feedback.tutorId === tutorId),
        underReviewByOtherTutor: entry.status === DiaryEntryStatus.SUBMIT && entry.reviewingTutorId && (!tutorId || entry.reviewingTutorId !== tutorId),
        reviewedByTutorName: entry.feedbacks?.[0]?.tutor?.name,
        score: entry.score,
        evaluatedAt: entry.evaluatedAt,
        moduleTitle: entry.settings?.settingsTemplate?.title || entry.settings?.title,
        moduleLevel: entry.settings?.settingsTemplate?.level || entry.settings?.level,
        wordLimit: entry.settings?.settingsTemplate?.wordLimit || entry.settings?.wordLimit,
        settingsTemplateId: entry.settings?.settingsTemplate?.id || entry.settings?.settingsTemplateId
      }));

      // Apply filters
      let filteredEntries = availableEntries.filter(entry => {
        // Filter out entries that are being reviewed by other tutors
        if (entry.underReviewByOtherTutor) {
          return false;
        }

        // Apply module filter if provided
        if (moduleFilter && (!entry.moduleTitle || !entry.moduleTitle.toLowerCase().includes(moduleFilter.toLowerCase()))) {
          return false;
        }

        // Apply student name filter if provided
        if (studentNameFilter && (!entry.studentName || !entry.studentName.toLowerCase().includes(studentNameFilter.toLowerCase()))) {
          return false;
        }

        // Apply date filters if provided
        if (dateFromFilter && (!entry.entryDate || new Date(entry.entryDate) < dateFromFilter)) {
          return false;
        }

        if (dateToFilter && (!entry.entryDate || new Date(entry.entryDate) > dateToFilter)) {
          return false;
        }

        return true;
      });

      // Sort entries if requested
      if (paginationDto && paginationDto.sortBy === 'moduleTitle') {
        filteredEntries.sort((a, b) => {
          const titleA = a.moduleTitle || '';
          const titleB = b.moduleTitle || '';
          return paginationDto.sortDirection === 'ASC'
            ? titleA.localeCompare(titleB)
            : titleB.localeCompare(titleA);
        });
      }

      // Sort entries by module level if requested
      if (paginationDto && paginationDto.sortBy === 'moduleLevel') {
        filteredEntries.sort((a, b) => {
          const levelA = a.moduleLevel || 0;
          const levelB = b.moduleLevel || 0;
          return paginationDto.sortDirection === 'ASC'
            ? levelA - levelB
            : levelB - levelA;
        });
      }

      this.logger.log(`[getPendingReviewEntries] After filtering, returning ${filteredEntries.length} entries`);
      return new PagedListDto(filteredEntries, totalCount);
    } catch (error) {
      this.logger.error('[getPendingReviewEntries] Error:', error.stack);
      throw error;
    }
  }

  /**
   * Pick a diary entry for review
   * @param entryId Entry ID
   * @param tutorId Tutor ID
   * @returns Updated diary entry
   */
  async pickEntryForReview(entryId: string, tutorId: string): Promise<DiaryEntry> {
    // Check if the user is a tutor
    const user = await this.userRepository.findOne({
      where: { id: tutorId },
      relations: ['userRoles', 'userRoles.role'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${tutorId} not found`);
    }

    // Check if the user has the tutor role
    const isTutor = user.userRoles.some(userRole => userRole.role.name === 'tutor');
    if (!isTutor) {
      throw new ForbiddenException('Only tutors can pick diary entries for review');
    }

    const entry = await this.diaryEntryRepository.findOne({
      where: { id: entryId },
      relations: ['diary', 'skin', 'diary.user'],
    });

    if (!entry) {
      throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
    }

    // Check if the entry is available for review
    if (entry.status === DiaryEntryStatus.SUBMIT) {
      // Entry is in SUBMIT status, check if it's already being reviewed
      if (entry.reviewingTutorId) {
        // Check if the lock has expired
        const now = getCurrentUTCDate();
        if (!entry.reviewExpiryTime || entry.reviewExpiryTime > now) {
          throw new BadRequestException('This entry is currently being reviewed by another tutor');
        }
      }
    } else if (entry.status === DiaryEntryStatus.REVIEWED) {
      throw new BadRequestException('This entry has already been reviewed');
    } else if (entry.status === DiaryEntryStatus.CONFIRM) {
      throw new BadRequestException('This entry has already been confirmed');
    } else {
      throw new BadRequestException('This entry is not available for review');
    }

    // Mark the entry as being reviewed (still in SUBMIT status)
    entry.reviewStartTime = getCurrentUTCDate();

    // Set review expiry time to 2 hours from now in UTC
    const expiryTime = getCurrentUTCDate();
    expiryTime.setUTCHours(expiryTime.getUTCHours() + 2);
    entry.reviewExpiryTime = expiryTime;

    entry.reviewingTutorId = tutorId;

    return await this.diaryEntryRepository.save(entry);
  }
}
