import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DiaryService } from './diary.service';
import { DiaryAwardPeriod } from '../../database/entities/diary-award.entity';

@Injectable()
export class DiaryAwardsScheduler {
  private readonly logger = new Logger(DiaryAwardsScheduler.name);

  constructor(private readonly diaryService: DiaryService) {}

  // Run every Sunday at midnight (weekly awards)
  @Cron(CronExpression.EVERY_WEEK)
  async generateWeeklyAwards() {
    this.logger.log('Generating weekly diary awards...');
    try {
      await this.diaryService.generatePeriodAwards(DiaryAwardPeriod.WEEKLY);
      this.logger.log('Weekly diary awards generated successfully');
    } catch (error) {
      this.logger.error(`Error generating weekly diary awards: ${error.message}`, error.stack);
    }
  }

  // Run on the 1st day of every month at midnight (monthly awards)
  @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
  async generateMonthlyAwards() {
    this.logger.log('Generating monthly diary awards...');
    try {
      await this.diaryService.generatePeriodAwards(DiaryAwardPeriod.MONTHLY);
      this.logger.log('Monthly diary awards generated successfully');
    } catch (error) {
      this.logger.error(`Error generating monthly diary awards: ${error.message}`, error.stack);
    }
  }
}
