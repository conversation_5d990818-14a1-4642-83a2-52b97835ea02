import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON>o<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, OneToOne } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { NovelTopic } from './novel-topic.entity';
import { User } from './user.entity';
import { DiarySkin } from './diary-skin.entity';
import { NovelFeedback } from './novel-feedback.entity';
import { NovelCorrection } from './novel-correction.entity';

export enum NovelEntryStatus {
  NEW = 'new',
  SUBMITTED = 'submitted',
  UPDATED = 'updated',
  CORRECTION_GIVEN = 'correction_given',
  REVIEWED = 'reviewed',
  UNDER_REVIEW = 'under_review',
  CONFIRMED = 'confirmed'
}

@Entity()
export class NovelEntry extends AuditableBaseEntity {
  @Column({ name: 'topic_id' })
  topicId: string;

  @ManyToOne(() => NovelTopic, topic => topic.entries)
  @JoinColumn({ name: 'topic_id' })
  topic: NovelTopic;

  @Column({ name: 'student_id' })
  studentId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'student_id' })
  student: User;

  @Column({ name: 'content', type: 'text' })
  content: string;

  @Column({ name: 'word_count', type: 'integer', default: 0 })
  wordCount: number;

  @Column({
    name: 'status',
    type: 'enum',
    enum: NovelEntryStatus,
    default: NovelEntryStatus.NEW
  })
  status: NovelEntryStatus;

  @Column({ name: 'skin_id', nullable: true })
  skinId: string;

  @ManyToOne(() => DiarySkin, { nullable: true })
  @JoinColumn({ name: 'skin_id' })
  skin: DiarySkin;

  @Column({ name: 'submitted_at', type: 'timestamp', nullable: true })
  submittedAt: Date;

  @Column({ name: 'reviewed_at', type: 'timestamp', nullable: true })
  reviewedAt: Date;

  @OneToMany(() => NovelFeedback, feedback => feedback.entry)
  feedbacks: NovelFeedback[];

  @OneToOne(() => NovelCorrection, correction => correction.entry)
  correction: NovelCorrection;
}
