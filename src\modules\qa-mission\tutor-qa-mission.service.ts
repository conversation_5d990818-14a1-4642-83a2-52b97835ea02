import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In } from 'typeorm';
import { QATaskSubmissionMarking } from "src/database/entities/qa-task-submission-marking.entity";
import { QATaskSubmissions, QASubmissionStatus } from "src/database/entities/qa-task-submissions.entity";
import { QATaskSubmissionHistory } from "src/database/entities/qa-task-submission-history.entity";
import { NotificationHelperService } from '../notification/notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import { QATaskSubmissionMarkingDto, CreateQATaskSubmissionMarkingDto } from 'src/database/models/qa-mission.dto';
import { SubmissionStatus } from 'src/database/entities/essay-task-submissions.entity';

@Injectable()
export class TutorQAMissionService {
  private readonly logger = new Logger(TutorQAMissionService.name);

  constructor(
    @InjectRepository(QATaskSubmissions)
    private readonly qaTaskSubmissionsRepository: Repository<QATaskSubmissions>,
    @InjectRepository(QATaskSubmissionHistory)
    private readonly qaTaskSubmissionHistoryRepository: Repository<QATaskSubmissionHistory>,
    @InjectRepository(QATaskSubmissionMarking)
    private readonly qaTaskSubmissionMarkingRepository: Repository<QATaskSubmissionMarking>,
    private dataSource: DataSource,
    private readonly notificationHelper: NotificationHelperService,
    private readonly deeplinkService: DeeplinkService
  ) {}

  private toQATaskSubmissionMarkingDto(taskSubmissionMark: QATaskSubmissionMarking): QATaskSubmissionMarkingDto {
    return {
      id: taskSubmissionMark.id,
      submissionId: taskSubmissionMark.submissionId,
      submissionHistoryId: taskSubmissionMark.submissionHistoryId,
      submissionFeedback: taskSubmissionMark.submissionFeedback,
      score: taskSubmissionMark.score,
      taskRemarks: taskSubmissionMark.taskRemarks,
    };
  }

  async markQATaskSubmission(
    qaSubmissionMarkDto: CreateQATaskSubmissionMarkingDto
  ): Promise<QATaskSubmissionMarkingDto> {
    const { submissionId } = qaSubmissionMarkDto;

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const submissionRepo = queryRunner.manager.getRepository(QATaskSubmissions);
      const historyRepo = queryRunner.manager.getRepository(QATaskSubmissionHistory);
      const markingRepo = queryRunner.manager.getRepository(QATaskSubmissionMarking);

      const taskSubmission = await submissionRepo.findOne({
        where: {
          id: submissionId,
          status: QASubmissionStatus.SUBMITTED,
        },
      });

      if (!taskSubmission) {
        throw new NotFoundException('Task submission not found');
      }

      const taskSubmissionHistory = await historyRepo.findOne({
        where: {
          id: taskSubmission.latestSubmissionId,
          submissionId: submissionId,
        },
      });

      if (!taskSubmissionHistory) {
        throw new NotFoundException('Task submission history not found');
      }

      // Update submission status to MARKED
      taskSubmission.status = QASubmissionStatus.REVIEWED;
      await submissionRepo.save(taskSubmission);

      // Create marking record
      const taskSubmissionMark = markingRepo.create({
        submissionId: submissionId,
        submissionHistoryId: taskSubmissionHistory.id,
        submissionFeedback: qaSubmissionMarkDto.submissionFeedback,
        score: qaSubmissionMarkDto.score,
        taskRemarks: qaSubmissionMarkDto.taskRemarks,
      });

      const savedMark = await markingRepo.save(taskSubmissionMark);

      await queryRunner.commitTransaction();

      // Send notification to the student
      try {
        // Get the student ID from the submission
        const studentId = taskSubmission.createdBy;

        if (studentId) {
          // Generate deeplinks
          const webLink = this.deeplinkService.getWebLink(DeeplinkType.QA_REVIEW, {
            id: submissionId
          });

          const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.QA_REVIEW, {
            id: submissionId
          });

          // Create HTML content for rich notifications
          const htmlContent = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #333;">Q&A Submission Reviewed</h2>
              <p>Your Q&A submission has been reviewed by your tutor.</p>
              <p><strong>Score:</strong> ${qaSubmissionMarkDto.score}</p>
              <div style="margin: 20px 0;">
                <a href="${webLink}" style="background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; display: inline-block;">View Review</a>
              </div>
            </div>
          `;

          // Send notification
          await this.notificationHelper.notify(
            studentId,
            NotificationType.QA_REVIEW,
            'Q&A Submission Reviewed',
            `Your Q&A submission has been reviewed by your tutor.`,
            {
              relatedEntityId: submissionId,
              relatedEntityType: 'qa_submission',
              htmlContent: htmlContent,
              webLink: webLink,
              deepLink: deepLink,
              sendEmail: true,
              sendPush: true,
              sendInApp: true,
              sendMobile: true,
              sendSms: false,
              sendRealtime: false
            }
          );

          this.logger.log(`Sent notification to student ${studentId} for submission ${submissionId}`);
        }
      } catch (notificationError) {
        // Log the error but don't fail the marking process
        this.logger.error(`Error sending notification: ${notificationError.message}`, notificationError.stack);
      }

      return this.toQATaskSubmissionMarkingDto(savedMark);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to mark QA submission: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }
}


