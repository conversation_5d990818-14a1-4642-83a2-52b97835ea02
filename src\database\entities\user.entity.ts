import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rt, OneToMany, OneToOne, ManyToOne, JoinColumn } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { UserRole } from "./user-role.entity";
import { UserPlan } from "./user-plan.entity";
import { ProfilePicture } from "./profile-picture.entity";
import { DiarySkin } from "./diary-skin.entity";
import * as bcrypt from 'bcryptjs';
import { UserResponseDto } from '../models/users.dto';
import { calculateAge, formatToYYYYMMDD } from '../../common/utils/date-utils';

export enum UserType {
  ADMIN = 'admin',
  TUTOR = 'tutor',
  STUDENT = 'student'
}

@Entity()
export class User extends AuditableBaseEntity {
  @Column({ name: 'name' })
  name: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'email', unique: true })
  email: string;

  @Column({ name: 'password' })
  password: string;

  @OneToMany(() => UserRole, userRole => userRole.user)
  userRoles: UserRole[];

  @OneToMany(() => UserPlan, userPlan => userPlan.user)
  userPlans: UserPlan[];

  @OneToOne(() => ProfilePicture, profilePicture => profilePicture.user, { nullable: true })
  profilePictureEntity: ProfilePicture;

  // Helper method to get the active plan for this user
  getActivePlan(): UserPlan | null {
    if (!this.userPlans || this.userPlans.length === 0) {
      return null;
    }
    return this.userPlans.find(up => up.isActive) || null;
  }

  @Column({
    name: 'type',
    type: 'enum',
    enum: UserType,
    default: UserType.STUDENT
  })
  type: UserType;

  @Column({ name: 'is_confirmed', default: false })
  isConfirmed: boolean;

  @Column({ name: 'is_active', default: false })
  isActive: boolean;

  @Column({ name: 'last_login_at', nullable: true })
  lastLoginAt: Date;

  // Profile fields
  @Column({ name: 'profile_picture', nullable: true })
  profilePicture: string;

  @Column({ name: 'phone_number', nullable: false })
  phoneNumber: string;

  // Alias for phoneNumber to support notification system
  get phone(): string {
    return this.phoneNumber;
  }

  @Column({ name: 'address', nullable: true })
  address: string;

  @Column({ name: 'city', nullable: true })
  city: string;

  @Column({ name: 'state', nullable: true })
  state: string;

  @Column({ name: 'country', nullable: true })
  country: string;

  @Column({ name: 'postal_code', nullable: true })
  postalCode: string;

  @Column({ name: 'bio', nullable: true })
  bio: string;

  @Column({ name: 'date_of_birth', type: 'date', nullable: true })
  dateOfBirth: Date;

  @Column({ name: 'gender', nullable: false })
  gender: string;

  @Column({ name: 'agreed_to_terms', default: false })
  agreedToTerms: boolean;

  @Column({ name: 'refresh_token', nullable: true })
  refreshToken: string;

  @Column({ name: 'refresh_token_expiry', nullable: true })
  refreshTokenExpiry: Date;

  @Column({ name: 'social_links', type: 'json', nullable: true })
  socialLinks: { [key: string]: string };

  // Default skin preferences
  @Column({ name: 'default_diary_skin_id', nullable: true })
  defaultDiarySkinId?: string;

  @Column({ name: 'default_novel_skin_id', nullable: true })
  defaultNovelSkinId?: string;

  // Relations for default skins
  @ManyToOne(() => DiarySkin, { nullable: true })
  @JoinColumn({ name: 'default_diary_skin_id' })
  defaultDiarySkin?: DiarySkin;

  @ManyToOne(() => DiarySkin, { nullable: true })
  @JoinColumn({ name: 'default_novel_skin_id' })
  defaultNovelSkin?: DiarySkin;

  @BeforeInsert()
  hashPassword() {
    // Only hash the password if it hasn't been hashed already
    if (this.password && !this.password.startsWith('$2a$')) {
      // Use synchronous method for simplicity
      this.password = bcrypt.hashSync(this.password, 10);
    }
  }

  /**
   * Explicitly hash the password - use this when creating users manually
   * @param plainPassword The plain text password to hash
   */
  setPassword(plainPassword: string) {
    if (!plainPassword) {
      return null;
    }

    // Use synchronous method for simplicity
    this.password = bcrypt.hashSync(plainPassword, 10);

    return this.password;
  }

  /**
   * Verify if the provided password matches the stored hash
   * @param plainPassword The plain text password to verify
   * @returns True if the password matches, false otherwise
   */
  verifyPassword(plainPassword: string): boolean {
    try {
      // Check if the password is empty or null
      if (!plainPassword) {
        return false;
      }

      // Check if the stored hash is in the correct format
      if (!this.password || !this.password.startsWith('$2a$')) {
        return false;
      }

      // Use synchronous method for simplicity
      return bcrypt.compareSync(plainPassword, this.password);
    } catch (error) {
      return false;
    }
  }

  /**
   * Format a Date object or string to YYYY-MM-DD string in UTC
   * @param date Date object or string to format
   * @returns Formatted date string in YYYY-MM-DD format
   */
  private formatDateOfBirth(date: Date | string): string | null {
    if (!date) return null;

    // If date is already a string, check if it's in YYYY-MM-DD format
    if (typeof date === 'string') {
      // Check if the string is in YYYY-MM-DD format
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (dateRegex.test(date)) {
        return date; // Already in the correct format
      }

      // Try to parse the string to a Date object
      try {
        const parsedDate = new Date(date);
        if (!isNaN(parsedDate.getTime())) {
          return formatToYYYYMMDD(parsedDate);
        }
      } catch (error) {
        return null;
      }

      return null;
    }

    // If it's a Date object, format it
    try {
      return formatToYYYYMMDD(date);
    } catch (error) {
      return null;
    }
  }

  toDto(selectedRole?: UserType): UserResponseDto {
    // Create the base DTO with common fields
    const dto: UserResponseDto = {
      id: this.id,
      name: this.name,
      userId: this.userId,
      email: this.email,
      type: this.type,
      isActive: this.isActive,
      isConfirmed: this.isConfirmed,
      roles: this.userRoles ? this.userRoles.map(userRole => userRole.toDto()) : [],
      defaultSkinId: this['_defaultSkinId'] || null,
      defaultDiarySkinId: this.defaultDiarySkinId || null,
      defaultNovelSkinId: this.defaultNovelSkinId || null,
      activePlan: this.getActivePlan()?.plan?.name || null,
      activePlanDetails: this.getActivePlan()?.toSimpleObject() || null,
      selectedRole: selectedRole || this.type, // Default to user's type if no selectedRole is provided
      profilePicture: this.profilePicture || null,
      profilePictureUrl: this['_profilePictureUrl'] || null,
      phoneNumber: this.phoneNumber,
      address: this.address,
      city: this.city,
      state: this.state,
      country: this.country,
      postalCode: this.postalCode,
      bio: this.bio,
      dateOfBirth: this.dateOfBirth ? this.formatDateOfBirth(this.dateOfBirth) : null,
      age: this.dateOfBirth ? (typeof this.dateOfBirth === 'string' ? calculateAge(this.dateOfBirth) : calculateAge(this.dateOfBirth)) : null,
      gender: this.gender,
      socialLinks: this.socialLinks,
      lastLoginAt: this.lastLoginAt,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      userRoles: this.userRoles ? this.userRoles.map(ur => ({
        userId: ur.userId,
        roleId: ur.roleId,
        roleName: ur.role?.name || 'Unknown',
        createdAt: ur.createdAt,
        updatedAt: ur.updatedAt
      })) : []
    };

    // Add user type-specific fields
    if (this.type === UserType.STUDENT) {
      // Add student-specific fields
      dto.assignedTutors = this['_assignedTutors'] || [];
      dto.ownedSkins = this['_ownedSkins'] || [];
    } else if (this.type === UserType.TUTOR) {
      // Add tutor-specific fields
      dto.assignedStudents = this['_assignedStudents'] || [];
      dto.assignedModules = this['_assignedModules'] || [];
      dto.education = this['_education'] || [];
    }

    return dto;
  }
}
