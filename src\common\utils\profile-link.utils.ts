import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserType } from '../../database/entities/user.entity';

/**
 * Utility service for generating profile links
 * This service provides a unified approach for generating profile links
 * across the application, including web links, deep links, and email links.
 */
@Injectable()
export class ProfileLinkService {
  private readonly logger = new Logger(ProfileLinkService.name);
  private readonly frontendUrl: string;
  private readonly mobileAppUrl: string;

  constructor(private readonly configService: ConfigService) {
    // Initialize URLs from config with fallbacks
    this.frontendUrl = this.getBaseUrl();
    this.mobileAppUrl = this.getMobileAppUrl();

    this.logger.log(`Initialized ProfileLinkService with frontendUrl: ${this.frontendUrl}, mobileAppUrl: ${this.mobileAppUrl}`);
  }

  /**
   * Get the base URL for the frontend
   * @returns The base URL for the frontend
   */
  private getBaseUrl(): string {
    let baseUrl = this.configService.get<string>('FRONTEND_URL') || 'http://103.209.40.213:3011';

    // Remove trailing slash if present
    if (baseUrl.endsWith('/')) {
      baseUrl = baseUrl.slice(0, -1);
    }

    return baseUrl;
  }

  /**
   * Get the base URL for mobile app deep links
   * @returns The base URL for mobile app deep links
   */
  private getMobileAppUrl(): string {
    let mobileUrl = this.configService.get<string>('MOBILE_APP_URL') || 'hecapp://';

    // Ensure the URL ends with a slash for consistency
    if (!mobileUrl.endsWith('/')) {
      mobileUrl = `${mobileUrl}/`;
    }

    return mobileUrl;
  }

  /**
   * Generate a profile link for a user
   * @param userId The user ID
   * @param userType The user type (admin, tutor, student)
   * @returns A URL to the user's profile
   */
  getProfileLink(userId: string, userType: UserType | string): string {
    if (!userId) {
      this.logger.warn('getProfileLink called with empty userId');
      return this.frontendUrl;
    }

    switch (userType) {
      case UserType.ADMIN:
        return `${this.frontendUrl}/admin-profile/${userId}`;

      case UserType.TUTOR:
        return `${this.frontendUrl}/tutor-profile/${userId}`;

      case UserType.STUDENT:
        return `${this.frontendUrl}/student-profile/${userId}`;

      default:
        // Default to a generic profile page
        return `${this.frontendUrl}/profile/${userId}`;
    }
  }

  /**
   * Generate a deep link for a user profile (for mobile apps)
   * @param userId The user ID
   * @param userType The user type (admin, tutor, student)
   * @returns A deep link URL to the user's profile in the mobile app
   */
  getProfileDeepLink(userId: string, userType: UserType | string): string {
    if (!userId) {
      this.logger.warn('getProfileDeepLink called with empty userId');
      return this.mobileAppUrl;
    }

    const path = this.getProfilePath(userType, userId);
    return `${this.mobileAppUrl}${path}`;
  }

  /**
   * Get the profile path based on user type
   * @param userType The user type
   * @param userId The user ID
   * @returns The path to the user's profile
   */
  private getProfilePath(userType: UserType | string, userId: string): string {
    switch (userType) {
      case UserType.ADMIN:
        return `admin-profile/${userId}`;

      case UserType.TUTOR:
        return `tutor-profile/${userId}`;

      case UserType.STUDENT:
        return `student-profile/${userId}`;

      default:
        // Default to a generic profile page
        return `profile/${userId}`;
    }
  }

  /**
   * Generate an HTML link element for a user profile
   * @param userId The user ID
   * @param userType The user type
   * @param linkText The text to display in the link (defaults to "View Profile")
   * @param cssClass Optional CSS class to add to the link
   * @returns An HTML anchor tag linking to the user's profile
   */
  getProfileLinkHtml(
    userId: string,
    userType: UserType | string,
    linkText: string = 'View Profile',
    cssClass?: string
  ): string {
    const url = this.getProfileLink(userId, userType);
    const className = cssClass ? ` class="${cssClass}"` : '';

    return `<a href="${url}"${className}>${linkText}</a>`;
  }

  /**
   * Generate a button-styled HTML link for a user profile
   * @param userId The user ID
   * @param userType The user type
   * @param buttonText The text to display on the button (defaults to "View Profile")
   * @returns An HTML anchor tag styled as a button
   */
  getProfileButtonHtml(
    userId: string,
    userType: UserType | string,
    buttonText: string = 'View Profile'
  ): string {
    const url = this.getProfileLink(userId, userType);

    return `<a href="${url}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block; margin: 10px 0;">${buttonText}</a>`;
  }
}
