import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';

/**
 * Types of notifications in the system
 * @enum {string}
 */
export enum NotificationType {
  /** Notification for diary entry submission */
  DIARY_SUBMISSION = 'diary_submission',
  /** Notification for diary entry update */
  DIARY_UPDATE = 'diary_update',
  /** Notification for diary entry review */
  DIARY_REVIEW = 'diary_review',
  /** Notification for diary feedback */
  DIARY_FEEDBACK = 'diary_feedback',
  /** Notification for mission creation */
  MISSION_CREATED = 'mission_created',
  /** Notification for mission submission */
  MISSION_SUBMISSION = 'mission_submission',
  MISSION_SUBMISSION_UPDATED = 'mission_submission_updated',
  /** Notification for mission feedback */
  MISSION_FEEDBACK = 'mission_feedback',
  /** Notification for mission correction */
  MISSION_CORRECTION = 'mission_correction',
  /** Notification for mission review completion */
  MISSION_REVIEW_COMPLETE = 'mission_review_complete',
  /** Notification for tutor greeting */
  TUTOR_GREETING = 'tutor_greeting',
  /** Notification for tutor assignment */
  TUTOR_ASSIGNMENT = 'tutor_assignment',
  /** Notification for tutor verification */
  TUTOR_VERIFICATION = 'tutor_verification',
  /** Notification for chat message */
  CHAT_MESSAGE = 'chat_message',
  /** Notification for Q&A submission */
  QA_SUBMISSION = 'qa_submission',
  /** Notification for Q&A review */
  QA_REVIEW = 'qa_review',
  /** Notification for Q&A feedback */
  QA_FEEDBACK = 'qa_feedback',
  /** Notification for essay submission */
  ESSAY_SUBMISSION = 'essay_submission',
  /** Notification for essay review */
  ESSAY_REVIEW = 'essay_review',
  /** Notification for essay feedback */
  ESSAY_FEEDBACK = 'essay_feedback',
  /** Notification for story submission */
  STORY_SUBMISSION = 'story_submission',
  /** Notification for story review */
  STORY_REVIEW = 'story_review',
  /** Notification for novel submission */
  NOVEL_SUBMISSION = 'novel_submission',
  /** Notification for novel entry update */
  NOVEL_UPDATE = 'novel_update',
  /** Notification for novel review */
  NOVEL_REVIEW = 'novel_review',
  /** Notification for novel feedback */
  NOVEL_FEEDBACK = 'novel_feedback',
  /** System notification */
  SYSTEM = 'system',
}

@Entity()
export class Notification extends AuditableBaseEntity {
  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({
    name: 'type',
    type: 'enum',
    enum: NotificationType,
  })
  type: NotificationType;

  @Column({ name: 'title' })
  title: string;

  @Column({ name: 'message', type: 'text' })
  message: string;

  @Column({ name: 'related_entity_id', nullable: true })
  relatedEntityId: string;

  @Column({ name: 'related_entity_type', nullable: true })
  relatedEntityType: string;

  @Column({ name: 'is_read', default: false })
  isRead: boolean;

  @Column({ name: 'read_at', nullable: true })
  readAt: Date;
}
