import { 
  Controller, 
  Post, 
  Get,
  Patch,
  Param,
  Body, 
  Query,
  UseGuards, 
  ParseU<PERSON><PERSON>ipe,
  Delete,
  HttpCode,
  Put
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiBody, ApiQuery, ApiParam, ApiOkResponse, ApiUnauthorizedResponse, ApiNotFoundResponse, ApiBadRequestResponse, ApiForbiddenResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithPagedListType } from '../../common/decorators/api-response.decorator';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { QAMissionService } from './qa-mission.service';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { 
  CreateQAMissionDto, 
  QAMissionResponseDto,
  QAMissionPaginationDto,
  UpdateQAMissionDto,
  QATaskDto,
  AllQAMissionResponseDto,
  CreateAllQAMissionDto,
  UpdateQATaskDto,
  QAMissionListResponseDto
} from '../../database/models/qa-mission.dto';

@Controller('admin/qa-mission')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('Admin Q&A Mission')
export class AdminQAMissionController {
  constructor(private readonly qaMissionService: QAMissionService) {}

  // @Post('create')
  // @ApiOperation({ summary: 'Create a new QA mission with tasks' })
  // @ApiBody({
  //   type: CreateQAMissionDto,
  //   description: 'QA mission creation data with tasks',
  //   examples: {
  //     example1: {
  //       value: {
  //         timeFrequency: 'weekly',
  //         tasks: [
  //           {
  //             title: 'Grammar Questions',
  //             description: 'Answer the following questions about English grammar',
  //             wordLimitMinimum: 200,
  //             wordLimitMaximum: 500,
  //             timePeriodUnit: 1,
  //             deadline: 7,
  //             instructions: 'Provide examples for each answer',
  //             metaData: {
  //               week: '1',
  //               month: '4',
  //               year: '2025'
  //             }
  //           }
  //         ]
  //       }
  //     }
  //   }
  // })
  // @ApiOkResponseWithType(QAMissionResponseDto, 'QA mission created successfully')
  // @ApiErrorResponse(400, 'Invalid input data')
  // @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  // @ApiErrorResponse(403, 'Forbidden - Admin access required')
  // async create(
  //   @Body() createQAMissionDto: CreateQAMissionDto
  // ): Promise<ApiResponse<QAMissionResponseDto>> {
  //   const result = await this.qaMissionService.create(createQAMissionDto);
  //   return ApiResponse.success(
  //     result,
  //     'QA mission created successfully',
  //     201
  //   );
  // }

  @Post('create-mission')
  @ApiOperation({ summary: 'Create a new QA mission with tasks' })
  @ApiBody({
    type: CreateAllQAMissionDto,
    description: 'QA mission creation data with tasks',
    examples: {
      example1: {
        summary: 'Create a weekly mission',
        value: {
          timeFrequency: 'weekly',
          weekId: "90bdacaf-e385-4567-8e48-5f450266d262",
          tasks: [
            {
              title: 'Grammar Questions',
              description: 'Answer the following questions about English grammar',
              wordLimitMinimum: 200,
              wordLimitMaximum: 500,              
              deadline: 7,
              totalScore: 10,
              instructions: 'Provide examples for each answer'
            }
          ]
        }
      }
    }
  })
  @ApiOkResponseWithType(AllQAMissionResponseDto, 'QA mission created successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async createMission(
    @Body() createAllQAMissionDto: CreateAllQAMissionDto
  ): Promise<ApiResponse<AllQAMissionResponseDto>> {
    const result = await this.qaMissionService.createMission(createAllQAMissionDto);
    return ApiResponse.success(
      result,
      'QA mission created successfully',
      201
    );
  }

  // @Get()
  // @ApiOperation({ summary: 'Get all QA missions' })
  // @ApiQuery({
  //   name: 'page',
  //   required: false,
  //   type: Number,
  //   description: 'Page number',
  // })
  // @ApiQuery({
  //   name: 'limit',
  //   required: false,
  //   type: Number,
  //   description: 'Number of items per page',
  // })
  // @ApiQuery({
  //   name: 'sortBy',
  //   required: false,
  //   type: String,
  //   description: 'Field to sort by',
  // })
  // @ApiQuery({
  //   name: 'sortDirection',
  //   required: false,
  //   type: String,
  //   enum: ['ASC', 'DESC'],
  //   description: 'Sort direction',
  // })
  // @ApiQuery({
  //   name: 'timeFrequency',
  //   required: false,
  //   enum: ['weekly', 'monthly'],
  //   description: 'Filter by time frequency',
  // })
  // @ApiQuery({
  //   name: 'weekOrMonth',
  //   required: false,
  //   type: Number,
  //   description: 'Filter by week or month number',
  // })
  // @ApiQuery({
  //   name: 'title',
  //   required: false,
  //   type: String,
  //   description: 'Filter tasks by title',
  // })
  // @ApiOkResponseWithPagedListType(QAMissionResponseDto, 'QA missions retrieved successfully')
  // @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  // @ApiErrorResponse(403, 'Forbidden - Admin access required')
  // @ApiErrorResponse(400, 'Bad request - Invalid parameters')
  // async findAll(@Query() paginationDto?: QAMissionPaginationDto): Promise<ApiResponse<PagedListDto<QAMissionResponseDto>>> {
  //   const result = await this.qaMissionService.findAll(paginationDto);
  //   return ApiResponse.success(result, 'QA missions retrieved successfully');
  // }

  // @Get('task-list')
  // @ApiOperation({ summary: 'Get all QA missions' })
  // @ApiQuery({
  //   name: 'page',
  //   required: false,
  //   type: Number,
  //   description: 'Page number',
  // })
  // @ApiQuery({
  //   name: 'limit',
  //   required: false,
  //   type: Number,
  //   description: 'Number of items per page',
  // })
  // @ApiQuery({
  //   name: 'sortBy',
  //   required: false,
  //   type: String,
  //   description: 'Field to sort by',
  // })
  // @ApiQuery({
  //   name: 'sortDirection',
  //   required: false,
  //   type: String,
  //   enum: ['ASC', 'DESC'],
  //   description: 'Sort direction',
  // })
  // @ApiQuery({
  //   name: 'timeFrequency',
  //   required: false,
  //   enum: ['weekly', 'monthly'],
  //   description: 'Filter by time frequency',
  // })
  // @ApiQuery({
  //   name: 'weekOrMonth',
  //   required: false,
  //   type: Number,
  //   description: 'Filter by week or month number',
  // })
  // @ApiQuery({
  //   name: 'title',
  //   required: false,
  //   type: String,
  //   description: 'Filter tasks by title',
  // })
  // @ApiOkResponseWithPagedListType(QAMissionResponseDto, 'QA missions retrieved successfully')
  // @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  // @ApiErrorResponse(403, 'Forbidden - Admin access required')
  // @ApiErrorResponse(400, 'Bad request - Invalid parameters')
  // async findList(@Query() paginationDto?: QAMissionPaginationDto): Promise<ApiResponse<PagedListDto<QAMissionResponseDto>>> {
  //   const result = await this.qaMissionService.findList(paginationDto);
  //   return ApiResponse.success(result, 'QA missions retrieved successfully');
  // }

  @Get('list')
  @ApiOperation({ summary: 'Get all QA missions' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    type: String,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction',
  })
  @ApiQuery({
    name: 'timeFrequency',
    required: false,
    enum: ['weekly', 'monthly'],
    description: 'Filter by time frequency',
  })
  @ApiQuery({
    name: 'weekOrMonth',
    required: false,
    type: Number,
    description: 'Filter by week or month number',
  })
  @ApiQuery({
    name: 'title',
    required: false,
    type: String,
    description: 'Filter tasks by title',
  })
  @ApiOkResponseWithPagedListType(QAMissionListResponseDto, 'QA missions retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(400, 'Bad request - Invalid parameters')
  async findList(@Query() paginationDto?: QAMissionPaginationDto): Promise<ApiResponse<PagedListDto<QAMissionListResponseDto>>> {
    const result = await this.qaMissionService.findMissionList(paginationDto);
    return ApiResponse.success(result, 'QA missions retrieved successfully');
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific QA mission by ID (Admin only)' })
  @ApiParam({ name: 'id', description: 'ID of the QA mission to retrieve', type: String })
  @ApiOkResponseWithType(QAMissionResponseDto, 'QA mission retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'QA mission not found')
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<QAMissionResponseDto>> {
    const result = await this.qaMissionService.findById(id);
    return ApiResponse.success(result, 'QA mission retrieved successfully');
  }
  
@Put('task/:id')
@ApiOperation({ summary: 'Update an existing QA task by ID (Admin only)' })
@ApiParam({ name: 'id', description: 'ID of the QA task to update', type: String })
@ApiBody({
  type: UpdateQATaskDto,
  description: 'Data to update the QA task. Provide only the fields you wish to update.',
  examples: {
    // example1: {
    //   summary: 'Update title and description',
    //   value: {
    //     title: 'Updated Task Title',
    //     description: 'Updated description of the task.'
    //   }
    // },
    // example2: {
    //   summary: 'Update word limits and deadline',
    //   value: {
    //     wordLimitMinimum: 150,
    //     wordLimitMaximum: 300,
    //     deadline: 5
    //   }
    // },
    example3: {
      summary: 'Update all fields',
      value: {
        "title": "Comprehensive Task Update",
        "description": "Detailed description of the task.",
        "wordLimitMinimum": 200,
        "wordLimitMaximum": 500,
        "deadline": 7,
        "instructions": "Follow the updated guidelines.",
        "totalScore": 100
      }
    }
  }
})
@ApiOkResponse({ type: QATaskDto, description: 'QA task updated successfully' })
@ApiBadRequestResponse({ description: 'Invalid input data or validation error' })
@ApiUnauthorizedResponse({ description: 'Unauthorized - Authentication required' })
@ApiForbiddenResponse({ description: 'Forbidden - Admin access required' })
@ApiNotFoundResponse({ description: 'QA task not found' })
  async updateTask(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Body() dto: UpdateQATaskDto,
  ) {
    const updatedTask = await this.qaMissionService.updateTaskById(id, dto);
    return {
      message: 'QA task updated successfully',
      data: updatedTask,
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update an existing QA mission' })
  @ApiParam({ name: 'id', description: 'ID of the QA mission to update', type: String })
  @ApiBody({ 
    type: UpdateQAMissionDto, 
    description: 'Data to update the QA mission. Include task IDs for updating existing tasks.',
    examples: {
      example1: {
        value: {
            "timeFrequency": "monthly",
            "monthId": "81d89f97-a02b-48f5-8feb-d3d802c84141",
            "tasks": [
              {
                "id": "ed25d158-9efc-4f1c-91a1-93078492160c",
                "title": "Grammar Questions",
                "description": "Answer the following questions about English grammar",
                "wordLimitMinimum": 150,
                "wordLimitMaximum": 500,
                "deadline": 4,
                "totalScore": 20,
                "instructions": "Provide examples for each answer"
            }
          ]          
        }
      }
    }
  })
  @ApiOkResponseWithType(QAMissionResponseDto, 'QA mission updated successfully')
  @ApiErrorResponse(400, 'Invalid input data or validation error')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'QA mission not found')
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateQAMissionDto: UpdateQAMissionDto
  ): Promise<ApiResponse<QAMissionResponseDto>> {
    const result = await this.qaMissionService.update(id, updateQAMissionDto);
    return ApiResponse.success(
      result,
      'QA mission updated successfully'
    );
  }

  // @Delete(':id/soft')
  // @UseGuards(JwtAuthGuard, AdminGuard)
  // @HttpCode(204)
  // @ApiOperation({ summary: 'Soft delete a QA mission and its tasks' })
  // @ApiErrorResponse(400, 'Failed to soft delete mission')
  // @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  // @ApiErrorResponse(403, 'Forbidden - Admin access required')
  // @ApiErrorResponse(404, 'Mission not found')
  // async softDelete(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<void>> {
  //   await this.qaMissionService.softDelete(id);
  //   return ApiResponse.success(null, 'QA mission soft deleted successfully');
  // }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @HttpCode(204)
  @ApiOperation({ summary: 'Permanently delete a QA mission and its tasks' })
  @ApiErrorResponse(400, 'Failed to delete mission')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Mission not found')
  async hardDelete(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<void>> {
    await this.qaMissionService.hardDelete(id);
    return ApiResponse.success(null, 'QA mission deleted successfully');
  }

  @Delete('task/:id')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @HttpCode(204)
  @ApiOperation({ summary: 'Permanently delete a QA task and its submissions' })
  @ApiErrorResponse(400, 'Failed to delete task')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Task not found')
  async hardDeleteTask(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<void>> {
    await this.qaMissionService.hardDeleteTask(id);
    return ApiResponse.success(null, 'QA task deleted successfully');
  }

  // @Delete('task/:id/soft')
  // @UseGuards(JwtAuthGuard, AdminGuard)
  // @HttpCode(204)
  // @ApiOperation({ summary: 'Soft delete a QA task and its submissions' })
  // @ApiErrorResponse(400, 'Failed to soft delete task')
  // @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  // @ApiErrorResponse(403, 'Forbidden - Admin access required')
  // @ApiErrorResponse(404, 'Task not found')
  // async softDeleteTask(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<void>> {
  //   await this.qaMissionService.softDeleteTask(id);
  //   return ApiResponse.success(null, 'QA task deleted successfully');
  // }

  @Get('task/:id')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiOperation({ summary: 'Get a specific QA task by ID (Admin only)' })
  @ApiParam({ name: 'id', description: 'ID of the QA task to retrieve', type: String })
  @ApiOkResponseWithType(QATaskDto, 'QA task retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'QA task not found')
  async findTask(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<any>> {
    const result = await this.qaMissionService.findTaskById(id);
    return ApiResponse.success(result, 'QA task retrieved successfully');
  }
}
