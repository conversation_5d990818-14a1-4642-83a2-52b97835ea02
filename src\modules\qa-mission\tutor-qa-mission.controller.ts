import {
  Controller,
  Post,
  Body,
  UseGuards,
} from "@nestjs/common";
import { ApiBearerAuth, ApiBody, ApiOperation, ApiTags } from "@nestjs/swagger";
import { JwtAuthGuard } from "src/common/guards/jwt.guard";
import { TutorGuard } from "src/common/guards/tutor.guard";
import { ApiOkResponseWithType, ApiErrorResponse } from 'src/common/decorators/api-response.decorator';
import { ApiResponse } from "src/common/dto/api-response.dto";
import { TutorQAMissionService } from "./tutor-qa-mission.service";
import { QATaskSubmissionMarkingDto, CreateQATaskSubmissionMarkingDto } from 'src/database/models/qa-mission.dto';

@ApiTags('Tutor Q&A Mission')
@ApiBearerAuth('JWT-auth')
@Controller('tutor-qa-mission')
export class TutorQAMissionController {
  constructor(
    private readonly tutorQAMissionService: TutorQAMissionService
  ){}

  @Post('QAMarking')
  @UseGuards(JwtAuthGuard, TutorGuard)
  @ApiOperation({ summary: 'Mark QA submission' })
  @ApiBody({
    type: CreateQATaskSubmissionMarkingDto,
    description: 'QA task submission marking data',
    examples: {
      'example1': {
        value: {
          submissionId: '123-456-789-abc-def-ghi',
          points: 85,
          submissionFeedback: 'Great job!',
          taskRemarks: 'Well done on the answers provided.',
        },
      },
  }})
  @ApiOkResponseWithType(QATaskSubmissionMarkingDto)
  @ApiErrorResponse(400, 'Bad Request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Not Found')
  async markQATaskSubmission(
    @Body() qaSubmissionMarkDto: CreateQATaskSubmissionMarkingDto
  ): Promise<ApiResponse<QATaskSubmissionMarkingDto>> {
    const result = await this.tutorQAMissionService.markQATaskSubmission(qaSubmissionMarkDto);
    return ApiResponse.success(result, "QA task submission marked successfully");
  }
}