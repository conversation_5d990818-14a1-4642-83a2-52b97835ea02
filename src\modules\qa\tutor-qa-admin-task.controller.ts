import { 
  Controller, 
  Get, 
  Put, 
  Param, 
  Body, 
  Query,
  UseGuards,
  Request, 
  Post,
  Delete,
  ParseUUIDPipe
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiQuery, ApiBody, ApiParam } from '@nestjs/swagger';
import { QAService } from './qa.service';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { TutorGuard } from '../../common/guards/tutor.guard';
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithPagedListType, ApiOkResponseWithArrayType } from '../../common/decorators/api-response.decorator';
import { 
  ReviewSubmissionDto,
  QASubmissionResponseDto,
  CreateQAQuestionDto,
  QAQuestionResponseDto,
  QAQuestionPaginationDto,
  CreateQAAssignmentDto,
  QAAssignmentResponseDto,
  UpdateQAQuestionDto
} from '../../database/models/qa.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { StudentDropdownDto } from 'src/database/models/qa/student-dropdown.dto';
import { TutorQAService } from './tutor-qa.service';
import { TutorStudentDto } from 'src/database/models/tutor-matching.dto';
import { GetUser } from 'src/common/decorators/get-user.decorator';
import { User } from 'src/database/entities/user.entity';
import { TutorPermissionGuard } from 'src/common/guards/tutor-permission.guard';

@ApiTags('Tutor Q&A Administrative Task')
@Controller('tutor/qa-admin')
@UseGuards(JwtAuthGuard, TutorPermissionGuard)
@ApiBearerAuth('JWT-auth')
export class TutorQAAdministrationController {
  constructor(
    private readonly tutorQAService: TutorQAService,
    private readonly qaService: QAService
  ) {}

  @Post('create')
  @ApiOperation({ summary: 'Create a new QA question' })
  @ApiBody({
    type: CreateQAQuestionDto,
    description: 'QA question creation data',
    examples: {
      example1: {
        value: {
          question: 'What are the key principles of Object-Oriented Programming?',
          points: 10,
          minimumWords: 200,
          isActive: true
        }
      }
    }
  })
  @ApiOkResponseWithType(QAQuestionResponseDto, 'QA question created successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Access required')
  
  async create(
    @Body() createQuestionDto: CreateQAQuestionDto,
    @Request() req
    ): Promise<ApiResponse<QAQuestionResponseDto>> {
    console.log('Creating question with data:', createQuestionDto);
    const result = await this.qaService.create(createQuestionDto, req.user.id);
    return ApiResponse.success(
      result,
      'QA question created successfully',
      201
    );
  }

  @Get('questions')
    @ApiOperation({ summary: 'Get all QA questions' })
    @ApiQuery({
      name: 'page',
      required: false,
      type: Number,
      description: 'Page number',
    })
    @ApiQuery({
      name: 'limit',
      required: false,
      type: Number,
      description: 'Number of items per page',
    })
    @ApiQuery({
      name: 'sortBy',
      required: false,
      type: String,
      description: 'Field to sort by',
    })
    @ApiQuery({
      name: 'sortDirection',
      required: false,
      type: String,
      enum: ['ASC', 'DESC'],
      description: 'Sort direction',
    })
    @ApiOkResponseWithPagedListType(QAQuestionResponseDto, 'Questions retrieved successfully')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Tutor access required')
    async getAllQuestions(
      @Query() paginationDto: QAQuestionPaginationDto
    ): Promise<ApiResponse<PagedListDto<QAQuestionResponseDto>>> {
      return this.qaService.getAllQuestions(paginationDto);
  }

  @Put('questions/:id')
  @ApiOperation({
    summary: 'Update a QA question (Admin only)',
    description: 'Updates an existing question.'
  })
  @ApiOkResponseWithType(QAQuestionResponseDto, 'Question updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Question not found')
  async updateQuestion(
    @Param('id') id: string,
    @Body() updateQuestionDto: UpdateQAQuestionDto
  ): Promise<ApiResponse<QAQuestionResponseDto>> {
    const question = await this.qaService.updateQuestion(id, updateQuestionDto);
    return ApiResponse.success(question, 'Question updated successfully');
  }

  @Delete('questions/:id')
  @ApiOperation({
    summary: 'Delete a QA question (Admin only)',
    description: 'Deletes an existing question.'
  })
  @ApiOkResponseWithType(QAQuestionResponseDto, 'Question deleted successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Question not found')
  async deleteQuestion(
    @Param('id') id: string
  ): Promise<ApiResponse<QAQuestionResponseDto>> {
    const question = await this.qaService.deleteQuestion(id);
    return ApiResponse.success(null, 'Question deleted successfully');
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific QA question by ID (Admin only)' })
  @ApiParam({ name: 'id', description: 'ID of the QA question to retrieve', type: String })
  @ApiOkResponseWithType(QAQuestionResponseDto, 'QA question retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'QA question not found')
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<QAQuestionResponseDto>> {
    const result = await this.qaService.findById(id);
    return ApiResponse.success(result, 'QA question retrieved successfully');
  }
}