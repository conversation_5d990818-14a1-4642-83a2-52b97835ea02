import { Injectable, NotFoundException, ConflictException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In, Not, IsNull } from 'typeorm';
import { Award, AwardModule } from '../../database/entities/award.entity';
import { AwardWinner } from '../../database/entities/award-winner.entity';
import { RewardPoint, RewardPointSource, RewardPointType } from '../../database/entities/reward-point.entity';
import { User } from '../../database/entities/user.entity';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { AwardCriteriaConfig, AWARD_CRITERIA, getCriteriaForModule, getModuleCriteriaMap } from '../../constants/award-criteria.constant';
import {
  CreateAwardDto,
  UpdateAwardDto,
  AwardResponseDto,
  CreateRewardPointDto,
  RewardPointResponseDto,
  UserRewardPointBalanceDto,
  CreateAwardWinnerDto,
  AwardWinnerResponseDto,
  UserAwardsResponseDto
} from '../../database/models/award.dto';
import { AwardFrequency } from '../../database/entities/award.entity';

@Injectable()
export class AwardsService {
  private readonly logger = new Logger(AwardsService.name);

  constructor(
    @InjectRepository(Award)
    private readonly awardRepository: Repository<Award>,
    @InjectRepository(AwardWinner)
    private readonly awardWinnerRepository: Repository<AwardWinner>,
    @InjectRepository(RewardPoint)
    private readonly rewardPointRepository: Repository<RewardPoint>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Create a new award
   * @param createAwardDto Award creation data
   * @returns Created award
   */
  async createAward(createAwardDto: CreateAwardDto): Promise<AwardResponseDto> {
    try {
      // Check if award with same name already exists
      const existingAward = await this.awardRepository.findOne({
        where: { name: createAwardDto.name }
      });

      if (existingAward) {
        throw new ConflictException(`Award with name '${createAwardDto.name}' already exists`);
      }

      // Validate award criteria
      this.validateAwardCriteria(createAwardDto.module, createAwardDto.criteria, createAwardDto.criteriaConfig);

      // Create new award
      const award = this.awardRepository.create({
        ...createAwardDto,
        startDate: createAwardDto.startDate ? new Date(createAwardDto.startDate) : null,
        endDate: createAwardDto.endDate ? new Date(createAwardDto.endDate) : null,
      });

      // Save award
      const savedAward = await this.awardRepository.save(award);

      return this.mapAwardToDto(savedAward);
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      this.logger.error(`Error creating award: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to create award');
    }
  }

  /**
   * Get all awards
   * @param module Optional module filter
   * @param includeInactive Whether to include inactive awards
   * @param name Optional name filter (partial match)
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of awards
   */
  async getAllAwards(
    module?: AwardModule,
    includeInactive: boolean = false,
    name?: string,
    paginationDto?: PaginationDto,
    frequency?: AwardFrequency,
  ): Promise<PagedListDto<AwardResponseDto>> {
    try {
      // Use query builder for more complex filtering
      const queryBuilder = this.awardRepository.createQueryBuilder('award');

      // Apply filters
      if (module) {
        queryBuilder.andWhere('award.module = :module', { module });
      }

      if (!includeInactive) {
        queryBuilder.andWhere('award.isActive = :isActive', { isActive: true });
      }

      // Add name filter with partial matching
      if (name) {
        // Use partial matching for name if at least 3 characters
        if (name.length >= 3) {
          queryBuilder.andWhere('LOWER(award.name) LIKE LOWER(:name)', { name: `%${name}%` });
        } else {
          queryBuilder.andWhere('LOWER(award.name) = LOWER(:name)', { name });
        }
      }

      if(frequency) queryBuilder.andWhere('award.frequency = :frequency', { frequency });

      // Get total count for pagination
      const totalCount = await queryBuilder.getCount();

      // Apply sorting and pagination
      if (paginationDto) {
        const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto;
        const skip = (page - 1) * limit;

        queryBuilder.skip(skip).take(limit);

        if (sortBy && sortDirection) {
          queryBuilder.orderBy(`award.${sortBy}`, sortDirection as 'ASC' | 'DESC');
        } else {
          queryBuilder.orderBy('award.createdAt', 'DESC');
        }
      } else {
        queryBuilder.orderBy('award.createdAt', 'DESC');
      }

      // Execute the query
      const awards = await queryBuilder.getMany();

      return new PagedListDto(
        awards.map(award => this.mapAwardToDto(award)),
        totalCount
      );
    } catch (error) {
      this.logger.error(`Error getting awards: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get awards');
    }
  }

  /**
   * Get award by ID
   * @param id Award ID
   * @returns Award details
   */
  async getAwardById(id: string): Promise<AwardResponseDto> {
    try {
      const award = await this.awardRepository.findOne({
        where: { id: id }
      });

      if (!award) {
        throw new NotFoundException(`Award with ID ${id} not found`);
      }

      return this.mapAwardToDto(award);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error getting award: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get award');
    }
  }

  /**
   * Update an award
   * @param id Award ID
   * @param updateAwardDto Award update data
   * @returns Updated award
   */
  async updateAward(id: string, updateAwardDto: UpdateAwardDto): Promise<AwardResponseDto> {
    try {
      // Check if award exists
      const award = await this.awardRepository.findOne({
        where: { id: id }
      });

      if (!award) {
        throw new NotFoundException(`Award with ID ${id} not found`);
      }

      // Check if name is being changed and if new name already exists
      if (updateAwardDto.name && updateAwardDto.name !== award.name) {
        const existingAward = await this.awardRepository.findOne({
          where: { name: updateAwardDto.name, id: Not(id) }
        });

        if (existingAward) {
          throw new ConflictException(`Award with name '${updateAwardDto.name}' already exists`);
        }
      }

      // Validate award criteria
      this.validateAwardCriteria(updateAwardDto.module, updateAwardDto.criteria, updateAwardDto.criteriaConfig);

      // Update award
      const updatedAward = {
        ...award,
        ...updateAwardDto,
        startDate: updateAwardDto.startDate ? new Date(updateAwardDto.startDate) : award.startDate,
        endDate: updateAwardDto.endDate ? new Date(updateAwardDto.endDate) : award.endDate,
      };

      // Save updated award
      const savedAward = await this.awardRepository.save(updatedAward);

      return this.mapAwardToDto(savedAward);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      this.logger.error(`Error updating award: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to update award');
    }
  }

  /**
   * Delete an award
   * @param id Award ID
   * @returns Success message
   */
  async deleteAward(id: string): Promise<{ success: boolean; message: string }> {
    try {
      // Check if award exists
      const award = await this.awardRepository.findOne({
        where: { id: id }
      });

      if (!award) {
        throw new NotFoundException(`Award with ID ${id} not found`);
      }

      // Check if award has been assigned to any users
      const awardWinners = await this.awardWinnerRepository.find({
        where: { awardId: id }
      });

      if (awardWinners.length > 0) {
        // Instead of deleting, mark as inactive
        award.isActive = false;
        await this.awardRepository.save(award);
        return { success: true, message: 'Award has been marked as inactive because it has been assigned to users' };
      }

      // Delete award
      await this.awardRepository.remove(award);
      return { success: true, message: 'Award deleted successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error deleting award: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to delete award');
    }
  }

  /**
   * Create a reward point transaction
   * @param createRewardPointDto Reward point creation data
   * @returns Created reward point transaction
   */
  async createRewardPoint(createRewardPointDto: CreateRewardPointDto): Promise<RewardPointResponseDto> {
    try {
      // Check if user exists
      const user = await this.userRepository.findOne({
        where: { id: createRewardPointDto.userId }
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${createRewardPointDto.userId} not found`);
      }

      // Create reward point
      const rewardPoint = this.rewardPointRepository.create({
        ...createRewardPointDto,
        expiryDate: createRewardPointDto.expiryDate ? new Date(createRewardPointDto.expiryDate) : null,
      });

      // Save reward point
      const savedRewardPoint = await this.rewardPointRepository.save(rewardPoint);

      return this.mapRewardPointToDto(savedRewardPoint);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error creating reward point: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to create reward point');
    }
  }

  /**
   * Get user reward point balance
   * @param userId User ID
   * @returns User reward point balance
   */
  async getUserRewardPointBalance(userId: string): Promise<UserRewardPointBalanceDto> {
    try {
      // Check if user exists
      const user = await this.userRepository.findOne({
        where: { id: userId }
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Get all reward points for user
      const rewardPoints = await this.rewardPointRepository.find({
        where: { userId: userId },
        order: { createdAt: 'DESC' }
      });

      // Calculate totals
      let totalPoints = 0;
      let availablePoints = 0;
      let spentPoints = 0;
      let expiredPoints = 0;

      for (const point of rewardPoints) {
        if (point.type === RewardPointType.EARNED || point.type === RewardPointType.ADJUSTED) {
          if (point.points > 0) {
            totalPoints += point.points;

            // Check if points have expired
            if (point.expiryDate && new Date() > point.expiryDate) {
              expiredPoints += point.points;
            } else {
              availablePoints += point.points;
            }
          } else {
            // Negative adjustments reduce available points
            availablePoints += point.points;
          }
        } else if (point.type === RewardPointType.SPENT) {
          spentPoints += point.points;
          availablePoints -= point.points;
        } else if (point.type === RewardPointType.EXPIRED) {
          expiredPoints += point.points;
        }
      }

      // Get recent transactions (last 10)
      const recentTransactions = rewardPoints.slice(0, 10).map(point => this.mapRewardPointToDto(point));

      return {
        userId,
        userName: user.name,
        totalPoints,
        availablePoints,
        spentPoints,
        expiredPoints,
        recentTransactions
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error getting user reward point balance: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get user reward point balance');
    }
  }

  /**
   * Create an award winner
   * @param createAwardWinnerDto Award winner creation data
   * @returns Created award winner
   */
  async createAwardWinner(createAwardWinnerDto: CreateAwardWinnerDto): Promise<AwardWinnerResponseDto> {
    try {
      // Check if user exists
      const user = await this.userRepository.findOne({
        where: { id: createAwardWinnerDto.userId }
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${createAwardWinnerDto.userId} not found`);
      }

      // Check if award exists
      const award = await this.awardRepository.findOne({
        where: { id: createAwardWinnerDto.awardId }
      });

      if (!award) {
        throw new NotFoundException(`Award with ID ${createAwardWinnerDto.awardId} not found`);
      }

      // Create award winner
      const awardWinner = this.awardWinnerRepository.create({
        ...createAwardWinnerDto,
        awardDate: new Date(createAwardWinnerDto.awardDate),
      });

      // Save award winner
      const savedAwardWinner = await this.awardWinnerRepository.save(awardWinner);

      // Create reward points for the award
      if (award.rewardPoints > 0) {
        const rewardPoint = this.rewardPointRepository.create({
          userId: createAwardWinnerDto.userId,
          source: this.mapModuleToSource(award.module),
          type: RewardPointType.EARNED,
          points: award.rewardPoints,
          referenceId: savedAwardWinner.id,
          description: `Award: ${award.name}`,
          // Set expiry date to 1 year from now
          expiryDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
        });

        await this.rewardPointRepository.save(rewardPoint);
      }

      return this.mapAwardWinnerToDto(savedAwardWinner, award, user);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error creating award winner: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to create award winner');
    }
  }

  /**
   * Get user awards
   * @param userId User ID
   * @returns User awards
   */
  async getUserAwards(userId: string): Promise<UserAwardsResponseDto> {
    try {
      // Check if user exists
      const user = await this.userRepository.findOne({
        where: { id: userId }
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Get all awards for user
      const awardWinners = await this.awardWinnerRepository.find({
        where: { userId: userId },
        relations: ['award'],
        order: { awardDate: 'DESC' }
      });

      // Calculate total reward points
      let totalRewardPoints = 0;
      const awards = [];

      for (const winner of awardWinners) {
        totalRewardPoints += winner.award.rewardPoints;
        awards.push(this.mapAwardWinnerToDto(winner, winner.award, user));
      }

      return {
        userId,
        userName: user.name,
        awards,
        totalAwards: awards.length,
        totalRewardPoints
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error getting user awards: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get user awards');
    }
  }

  /**
   * Get all award winners
   * @param awardId Optional award ID filter
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of award winners
   */
  async getAllAwardWinners(
    awardId?: string,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<AwardWinnerResponseDto>> {
    try {
      const whereConditions: any = {};

      if (awardId) {
        whereConditions.awardId = awardId;
      }

      // Get total count for pagination
      const totalCount = await this.awardWinnerRepository.count({
        where: whereConditions
      });

      // Apply pagination if provided
      let options: any = {
        where: whereConditions,
        relations: ['award', 'user'],
        order: { awardDate: 'DESC' }
      };

      if (paginationDto) {
        const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto;
        const skip = (page - 1) * limit;

        options.skip = skip;
        options.take = limit;

        if (sortBy && sortDirection) {
          options.order = { [sortBy]: sortDirection };
        }
      }

      const awardWinners = await this.awardWinnerRepository.find(options);

      return new PagedListDto(
        awardWinners.map(winner => this.mapAwardWinnerToDto(winner, winner.award, winner.user)),
        totalCount
      );
    } catch (error) {
      this.logger.error(`Error getting award winners: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get award winners');
    }
  }

  /**
   * Get all available award criteria with their configuration schemas
   * @returns List of award criteria
   */
  async getAwardCriteria(): Promise<AwardCriteriaConfig[]> {
    return AWARD_CRITERIA;
  }

  /**
   * Validate award criteria and configuration
   * @param module Award module
   * @param criteria Array of criteria
   * @param criteriaConfig Configuration for the criteria
   * @throws BadRequestException if validation fails
   */
  private validateAwardCriteria(module: AwardModule, criteria: string[], criteriaConfig: any): void {
    // Note: Basic criteria validation is now handled by the DTO validator
    // This method now focuses on business logic validation

    // Additional validation: Check for duplicate criteria
    const uniqueCriteria = [...new Set(criteria)];
    if (uniqueCriteria.length !== criteria.length) {
      throw new BadRequestException('Duplicate criteria are not allowed');
    }

    // Validate configuration for each criterion
    if (criteriaConfig) {
      // Basic validation - ensure criteriaConfig is an object
      if (typeof criteriaConfig !== 'object' || Array.isArray(criteriaConfig)) {
        throw new BadRequestException('Criteria configuration must be an object');
      }

      // Additional business logic validation can be added here
      // For example, validating specific configuration fields based on criteria
    }
  }

  /**
   * Map award entity to DTO
   * @param award Award entity
   * @returns Award DTO
   */
  private mapAwardToDto(award: Award): AwardResponseDto {
    return {
      id: award.id,
      name: award.name,
      description: award.description,
      module: award.module,
      criteria: award.criteria,
      frequency: award.frequency,
      rewardPoints: award.rewardPoints,
      isActive: award.isActive,
      criteriaConfig: award.criteriaConfig,
      startDate: award.startDate,
      endDate: award.endDate,
      imageUrl: award.imageUrl,
      createdAt: award.createdAt,
      updatedAt: award.updatedAt
    };
  }

  /**
   * Map reward point entity to DTO
   * @param rewardPoint Reward point entity
   * @returns Reward point DTO
   */
  private mapRewardPointToDto(rewardPoint: RewardPoint): RewardPointResponseDto {
    return {
      id: rewardPoint.id,
      userId: rewardPoint.userId,
      source: rewardPoint.source,
      type: rewardPoint.type,
      points: rewardPoint.points,
      referenceId: rewardPoint.referenceId,
      description: rewardPoint.description,
      expiryDate: rewardPoint.expiryDate,
      createdAt: rewardPoint.createdAt,
      updatedAt: rewardPoint.updatedAt
    };
  }

  /**
   * Map award winner entity to DTO
   * @param awardWinner Award winner entity
   * @param award Award entity
   * @param user User entity
   * @returns Award winner DTO
   */
  private mapAwardWinnerToDto(awardWinner: AwardWinner, award: Award, user: User): AwardWinnerResponseDto {
    return {
      id: awardWinner.id,
      userId: awardWinner.userId,
      userName: user.name,
      awardId: awardWinner.awardId,
      awardName: award.name,
      awardDate: awardWinner.awardDate,
      awardReason: awardWinner.awardReason,
      metadata: awardWinner.metadata,
      rewardPoints: award.rewardPoints,
      createdAt: awardWinner.createdAt
    };
  }
  /**
   * Map award module to reward point source
   * @param module Award module
   * @returns Reward point source
   */
  private mapModuleToSource(module: AwardModule): RewardPointSource {
    switch (module) {
      case AwardModule.DIARY:
        return RewardPointSource.DIARY_AWARD;
      case AwardModule.NOVEL:
        return RewardPointSource.NOVEL;
      case AwardModule.ESSAY:
        return RewardPointSource.ESSAY;
      default:
        return RewardPointSource.ADMIN_ADJUSTMENT;
    }
  }
}
