import { Injectable, NotFoundException, BadRequestException, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { NovelEntry, NovelEntryStatus } from '../../../database/entities/novel-entry.entity';
import { NovelTopic } from '../../../database/entities/novel-topic.entity';
import { NovelModuleSkinPreference } from '../../../database/entities/novel-module-skin-preference.entity';
import { StudentTutorMapping, MappingStatus } from '../../../database/entities/student-tutor-mapping.entity';
import { User } from '../../../database/entities/user.entity';
import {
  UpdateNovelEntryDto,
  SubmitNovelEntryDto,
  NovelEntryResponseDto,
  SetNovelSkinPreferenceDto,
  NovelSkinPreferenceResponseDto
} from '../../../database/models/novel.dto';
import { NotificationHelperService } from '../../notification/notification-helper.service';
import { NotificationType } from '../../../database/entities/notification.entity';
import { DiarySkinService } from '../../diary/diary-skin.service';

@Injectable()
export class NovelEntryService {
  private readonly logger = new Logger(NovelEntryService.name);

  constructor(
    @InjectRepository(NovelEntry)
    private readonly novelEntryRepository: Repository<NovelEntry>,
    @InjectRepository(NovelTopic)
    private readonly novelTopicRepository: Repository<NovelTopic>,
    @InjectRepository(NovelModuleSkinPreference)
    private readonly skinPreferenceRepository: Repository<NovelModuleSkinPreference>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
    @Inject(forwardRef(() => NotificationHelperService))
    private readonly notificationHelper: NotificationHelperService,
    @Inject(forwardRef(() => DiarySkinService))
    private readonly diarySkinService: DiarySkinService
  ) {}



  async updateEntry(studentId: string, entryId: string, updateEntryDto: UpdateNovelEntryDto): Promise<NovelEntryResponseDto> {
    const entry = await this.novelEntryRepository.findOne({
      where: { id: entryId, studentId }
    });

    if (!entry) {
      throw new NotFoundException('Entry not found');
    }

    if (entry.status === NovelEntryStatus.REVIEWED || entry.status === NovelEntryStatus.CONFIRMED) {
      throw new BadRequestException('Cannot update entry that has been reviewed');
    }

    if (updateEntryDto.content) {
      entry.wordCount = this.calculateWordCount(updateEntryDto.content);
    }

    Object.assign(entry, updateEntryDto);
    const updatedEntry = await this.novelEntryRepository.save(entry);

    this.logger.log(`Updated novel entry ${entryId} for student ${studentId}`);
    return this.mapEntryToResponseDto(updatedEntry);
  }

  async submitEntry(studentId: string, submitEntryDto: SubmitNovelEntryDto): Promise<NovelEntryResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const entry = await this.novelEntryRepository.findOne({
        where: { id: submitEntryDto.entryId, studentId },
        relations: ['topic', 'student']
      });

      if (!entry) {
        throw new NotFoundException('Entry not found');
      }

      if (entry.status === NovelEntryStatus.REVIEWED || entry.status === NovelEntryStatus.CONFIRMED) {
        throw new BadRequestException('Entry has already been reviewed');
      }

      // Update content if provided
      if (submitEntryDto.content !== undefined) {
        entry.content = submitEntryDto.content;
        entry.wordCount = this.calculateWordCount(submitEntryDto.content);
      }

      // Update skin if provided
      if (submitEntryDto.skinId !== undefined) {
        entry.skinId = submitEntryDto.skinId;
      }

      // Update status and submission time
      const newStatus = entry.status === NovelEntryStatus.NEW ? NovelEntryStatus.SUBMITTED : NovelEntryStatus.UPDATED;
      entry.status = newStatus;
      entry.submittedAt = new Date();

      const updatedEntry = await queryRunner.manager.save(entry);
      await queryRunner.commitTransaction();

      // Send notification to tutor
      try {
        await this.sendSubmissionNotification(entry, newStatus);
      } catch (notificationError) {
        this.logger.error(`Failed to send notification: ${notificationError.message}`, notificationError.stack);
      }

      this.logger.log(`Submitted novel entry ${submitEntryDto.entryId} for student ${studentId}`);
      return this.mapEntryToResponseDto(updatedEntry);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error submitting novel entry: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async getStudentEntries(studentId: string, category?: string): Promise<NovelEntryResponseDto[]> {
    const queryBuilder = this.novelEntryRepository
      .createQueryBuilder('entry')
      .leftJoinAndSelect('entry.topic', 'topic')
      .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
      .leftJoinAndSelect('entry.correction', 'correction')
      .where('entry.studentId = :studentId', { studentId });

    if (category) {
      queryBuilder.andWhere('topic.category = :category', { category });
    }

    queryBuilder.orderBy('entry.createdAt', 'DESC');

    const entries = await queryBuilder.getMany();
    return entries.map(entry => this.mapEntryToResponseDto(entry));
  }

  async getEntryById(entryId: string, studentId?: string): Promise<NovelEntryResponseDto> {
    const whereCondition: any = { id: entryId };
    if (studentId) {
      whereCondition.studentId = studentId;
    }

    const entry = await this.novelEntryRepository.findOne({
      where: whereCondition,
      relations: ['topic', 'feedbacks', 'correction', 'student']
    });

    if (!entry) {
      throw new NotFoundException('Entry not found');
    }

    return this.mapEntryToResponseDto(entry);
  }

  async getOrCreateEntryByTopicId(studentId: string, topicId: string): Promise<NovelEntryResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Check if entry already exists for this student and topic
      let entry = await this.novelEntryRepository.findOne({
        where: { topicId, studentId },
        relations: ['topic', 'feedbacks', 'correction']
      });

      if (entry) {
        // Entry exists, return it
        await queryRunner.commitTransaction();
        this.logger.log(`Found existing entry for student ${studentId} on topic ${topicId}`);
        return this.mapEntryToResponseDto(entry);
      }

      // Entry doesn't exist, create a new one
      // First verify topic exists and is active
      const topic = await this.novelTopicRepository.findOne({
        where: { id: topicId, isActive: true }
      });

      if (!topic) {
        throw new NotFoundException('Topic not found or inactive');
      }

      // Get user's default novel skin, fall back to old preference system if needed
      let defaultSkinId: string | null = null;

      // First try to get from user entity
      const user = await this.userRepository.findOne({
        where: { id: studentId },
        select: ['defaultNovelSkinId']
      });
      defaultSkinId = user?.defaultNovelSkinId;

      // If no default novel skin in user entity, check old preference system
      if (!defaultSkinId) {
        const skinPreference = await this.skinPreferenceRepository.findOne({
          where: { studentId }
        });
        defaultSkinId = skinPreference?.defaultSkinId;
      }

      if (!defaultSkinId) {
        throw new BadRequestException('Please set a default skin for the Novel module before creating an entry');
      }

      // Create new entry with default content and default skin
      entry = this.novelEntryRepository.create({
        topicId,
        studentId,
        content: '', // Start with empty content
        wordCount: 0,
        skinId: defaultSkinId,
        status: NovelEntryStatus.NEW
      });

      const savedEntry = await queryRunner.manager.save(entry);

      // Load the topic relation for the response
      savedEntry.topic = topic;

      await queryRunner.commitTransaction();

      this.logger.log(`Created new entry for student ${studentId} on topic ${topicId}`);
      return this.mapEntryToResponseDto(savedEntry);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error getting or creating entry: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async setDefaultSkin(studentId: string, setSkinDto: SetNovelSkinPreferenceDto): Promise<NovelSkinPreferenceResponseDto> {
    let preference = await this.skinPreferenceRepository.findOne({
      where: { studentId }
    });

    if (preference) {
      preference.defaultSkinId = setSkinDto.defaultSkinId;
    } else {
      preference = this.skinPreferenceRepository.create({
        studentId,
        defaultSkinId: setSkinDto.defaultSkinId
      });
    }

    const savedPreference = await this.skinPreferenceRepository.save(preference);
    this.logger.log(`Set default skin for student ${studentId}`);

    return {
      id: savedPreference.id,
      studentId: savedPreference.studentId,
      defaultSkinId: savedPreference.defaultSkinId,
      createdAt: savedPreference.createdAt,
      updatedAt: savedPreference.updatedAt
    };
  }

  async getDefaultSkin(studentId: string): Promise<NovelSkinPreferenceResponseDto | null> {
    const preference = await this.skinPreferenceRepository.findOne({
      where: { studentId }
    });

    if (!preference) {
      return null;
    }

    return {
      id: preference.id,
      studentId: preference.studentId,
      defaultSkinId: preference.defaultSkinId,
      createdAt: preference.createdAt,
      updatedAt: preference.updatedAt
    };
  }

  /**
   * Set default novel skin for a user using the new user entity approach
   * @param userId User ID
   * @param skinId Skin ID
   */
  async setDefaultNovelSkin(userId: string, skinId: string): Promise<void> {
    // Validate that the user has access to this skin (use diary skin service validation)
    await this.diarySkinService.validateStudentSkinAccess(userId, skinId);

    // Update user's default novel skin
    await this.userRepository.update(userId, {
      defaultNovelSkinId: skinId
    });

    this.logger.log(`Updated default novel skin for user ${userId} to ${skinId}`);
  }

  /**
   * Get default novel skin for a user using the new user entity approach
   * @param userId User ID
   * @returns Default novel skin or null if not set
   */
  async getDefaultNovelSkin(userId: string): Promise<any | null> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['defaultNovelSkin']
    });

    if (!user?.defaultNovelSkin) {
      return null;
    }

    // Return a simplified skin response (novel uses diary skins)
    return {
      id: user.defaultNovelSkin.id,
      name: user.defaultNovelSkin.name,
      description: user.defaultNovelSkin.description,
      isActive: user.defaultNovelSkin.isActive,
      isGlobal: true,
      templateContent: user.defaultNovelSkin.templateContent,
      isUserDefaultNovel: true,
      isUserDefaultDiary: false
    };
  }

  private async sendSubmissionNotification(entry: NovelEntry, status: NovelEntryStatus): Promise<void> {
    try {
      // Find assigned tutor
      const tutorMapping = await this.studentTutorMappingRepository.findOne({
        where: {
          studentId: entry.studentId,
          status: MappingStatus.ACTIVE
        },
        relations: ['tutor']
      });

      if (tutorMapping) {
        const notificationType = status === NovelEntryStatus.SUBMITTED
          ? NotificationType.NOVEL_SUBMISSION
          : NotificationType.NOVEL_UPDATE;

        const title = status === NovelEntryStatus.SUBMITTED
          ? 'New Novel Submission'
          : 'Novel Entry Updated';

        const message = `${entry.student.name} has ${status === NovelEntryStatus.SUBMITTED ? 'submitted' : 'updated'} a novel entry for topic "${entry.topic.sequenceTitle}"`;

        await this.notificationHelper.notify(
          tutorMapping.tutorId,
          notificationType,
          title,
          message,
          {
            relatedEntityId: entry.id,
            relatedEntityType: 'novel_entry',
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: false
          }
        );
      }
    } catch (error) {
      this.logger.warn(`Failed to send notification: ${error.message}`);
    }
  }

  private calculateWordCount(content: string): number {
    return content.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  private mapEntryToResponseDto(entry: NovelEntry): NovelEntryResponseDto {
    return {
      id: entry.id,
      topicId: entry.topicId,
      studentId: entry.studentId,
      content: entry.content,
      wordCount: entry.wordCount,
      status: entry.status,
      skinId: entry.skinId,
      submittedAt: entry.submittedAt,
      reviewedAt: entry.reviewedAt,
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt,
      topic: entry.topic ? {
        id: entry.topic.id,
        title: entry.topic.title,
        sequenceTitle: entry.topic.sequenceTitle,
        category: entry.topic.category,
        instruction: entry.topic.instruction,
        isActive: entry.topic.isActive,
        createdAt: entry.topic.createdAt,
        updatedAt: entry.topic.updatedAt
      } : undefined,
      feedbacks: entry.feedbacks?.map(feedback => ({
        id: feedback.id,
        entryId: feedback.entryId,
        tutorId: feedback.tutorId,
        feedback: feedback.feedback,
        createdAt: feedback.createdAt,
        updatedAt: feedback.updatedAt
      })),
      correction: entry.correction ? {
        id: entry.correction.id,
        entryId: entry.correction.entryId,
        tutorId: entry.correction.tutorId,
        correction: entry.correction.correction,
        score: entry.correction.score,
        createdAt: entry.correction.createdAt,
        updatedAt: entry.correction.updatedAt
      } : undefined
    };
  }
}
