import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsNumber, IsPositive, IsOptional, IsBoolean, IsArray, ValidateNested, ArrayMinSize, ArrayMaxSize } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { PaginationDto } from '../../../common/models/pagination.dto';

/**
 * DTO for block game sentence
 */
export class BlockGameSentenceDto {
  @ApiProperty({
    description: 'The starting part of the sentence',
    example: 'The fan makes a',
  })
  @IsString()
  @IsNotEmpty()
  starting_part: string;

  @ApiProperty({
    description: 'The expanding part of the sentence',
    example: 'strange sound',
  })
  @IsString()
  @IsNotEmpty()
  expanding_part: string;

  @ApiProperty({
    description: 'The order of the sentence in the game',
    example: 1,
  })
  @IsNumber()
  @IsPositive()
  sentence_order: number;
}

/**
 * DTO for creating a new block game
 */
export class CreateBlockGameDto {
  @ApiProperty({
    description: 'The title of the block game',
    example: 'Basic Sentence Building',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'The total score for the block game',
    example: 20,
  })
  @IsNumber()
  @IsPositive()
  score: number;

  @ApiProperty({
    description: 'Array of sentences for the block game (max 10 sentences)',
    type: [BlockGameSentenceDto],
    isArray: true,
    example: [
      {
        starting_part: 'The fan makes a',
        expanding_part: 'strange sound',
        sentence_order: 1,
      },
      {
        starting_part: 'I will check',
        expanding_part: 'it tomorrow morning',
        sentence_order: 2,
      },
    ],
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one sentence must be provided' })
  @ArrayMaxSize(10, { message: 'Maximum 10 sentences are allowed' })
  @ValidateNested({ each: true })
  @Type(() => BlockGameSentenceDto)
  sentences: BlockGameSentenceDto[];
}

/**
 * DTO for updating a block game
 */
export class UpdateBlockGameDto {
  @ApiProperty({
    description: 'The title of the block game',
    example: 'Advanced Sentence Building',
    required: false,
  })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({
    description: 'The total score for the block game',
    example: 25,
    required: false,
  })
  @IsNumber()
  @IsPositive()
  @IsOptional()
  score?: number;

  @ApiProperty({
    description: 'Whether the block game is active and available to students',
    example: true,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  is_active?: boolean;

  @ApiProperty({
    description: 'Array of sentences for the block game (max 10 sentences)',
    type: [BlockGameSentenceDto],
    isArray: true,
    required: false,
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one sentence must be provided' })
  @ArrayMaxSize(10, { message: 'Maximum 10 sentences are allowed' })
  @ValidateNested({ each: true })
  @Type(() => BlockGameSentenceDto)
  @IsOptional()
  sentences?: BlockGameSentenceDto[];
}

/**
 * DTO for block game response
 */
export class BlockGameResponseDto {
  @ApiProperty({
    description: 'The ID of the block game',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The title of the block game',
    example: 'Basic Sentence Building',
  })
  title: string;

  @ApiProperty({
    description: 'The total score for the block game',
    example: 20,
  })
  score: number;

  @ApiProperty({
    description: 'Whether the block game is active and available to students',
    example: true,
  })
  is_active: boolean;

  @ApiProperty({
    description: 'The number of sentences in the block game',
    example: 5,
  })
  sentence_count: number;

  @ApiProperty({
    description: 'When the block game was created',
    example: '2023-01-01T00:00:00.000Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'When the block game was last updated',
    example: '2023-01-02T00:00:00.000Z',
  })
  updated_at: Date;

  @ApiProperty({
    description: 'Who created the block game',
    example: '<EMAIL>',
  })
  created_by: string;
}

/**
 * DTO for toggling block game active status
 */
export class ToggleBlockGameStatusDto {
  @ApiProperty({
    description: 'Whether the block game should be active',
    example: true,
  })
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  is_active: boolean;
}

/**
 * DTO for block game query parameters
 */
export class GetBlockGamesQueryDto extends PaginationDto {
  @ApiProperty({
    description: 'Search term for title',
    required: false,
    example: 'sentence',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Filter by active status',
    required: false,
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  is_active?: boolean;

  // Inherits sortBy and sortDirection from PaginationDto
}
