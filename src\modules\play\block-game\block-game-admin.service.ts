import { Injectable, BadRequestException, NotFoundException, Logger, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { BlockGame } from '../../../database/entities/block-game.entity';
import { BlockGameSentence } from '../../../database/entities/block-game-sentence.entity';
import { BlockGameAttempt } from '../../../database/entities/block-game-attempt.entity';
import { User, UserType } from '../../../database/entities/user.entity';
import { CreateBlockGameDto, UpdateBlockGameDto, BlockGameResponseDto, GetBlockGamesQueryDto, ToggleBlockGameStatusDto } from '../../../database/models/block-game/block-game.dto';
import {
  GetBlockGameParticipantsQueryDto,
  BlockGameParticipantsResponseDto,
  BlockGameParticipantDto,
  GetStudentBlockGameParticipationQueryDto,
  StudentBlockGameParticipationResponseDto,
  StudentBlockGameParticipationDto,
} from '../../../database/models/block-game/block-game-admin.dto';
import { PagedListDto } from '../../../common/models/paged-list.dto';

interface SentenceValidationError {
  index: number;
  sentence: any;
  errors: string[];
}

interface SentencesValidationResult {
  validSentences: any[];
  errors: SentenceValidationError[];
}

@Injectable()
export class BlockGameAdminService {
  private readonly logger = new Logger(BlockGameAdminService.name);

  constructor(
    @InjectRepository(BlockGame)
    private readonly blockGameRepository: Repository<BlockGame>,
    @InjectRepository(BlockGameSentence)
    private readonly blockGameSentenceRepository: Repository<BlockGameSentence>,
    @InjectRepository(BlockGameAttempt)
    private readonly blockGameAttemptRepository: Repository<BlockGameAttempt>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Get all block games with pagination and filtering
   */
  async getBlockGames(queryDto: GetBlockGamesQueryDto): Promise<PagedListDto<BlockGameResponseDto>> {
    const { page = 1, limit = 10, search, is_active, sortBy = 'createdAt', sortDirection = 'DESC' } = queryDto;

    const skip = (page - 1) * limit;

    try {
      // Optimized query - separate sentence count query for better performance
      const queryBuilder = this.blockGameRepository.createQueryBuilder('blockGame');

      if (search) {
        queryBuilder.andWhere('blockGame.title ILIKE :search', { search: `%${search}%` });
      }

      // Apply active status filter
      if (is_active !== undefined) {
        queryBuilder.andWhere('blockGame.isActive = :isActive', { isActive: is_active });
      }

      // Apply sorting
      const sortFieldMapping = {
        title: 'title',
        score: 'score',
        createdAt: 'createdAt',
        updatedAt: 'updatedAt',
      };
      const validSortBy = sortFieldMapping[sortBy] || 'createdAt';

      queryBuilder.orderBy(`blockGame.${validSortBy}`, sortDirection as 'ASC' | 'DESC');

      queryBuilder.skip(skip).take(limit);

      const [blockGames, totalCount] = await queryBuilder.getManyAndCount();

      // Get sentence counts for each block game in a separate optimized query
      const blockGameIds = blockGames.map((bg) => bg.id);
      const sentenceCounts =
        blockGameIds.length > 0
          ? await this.blockGameSentenceRepository
              .createQueryBuilder('sentence')
              .select('sentence.blockGameId', 'blockGameId')
              .addSelect('COUNT(*)', 'count')
              .where('sentence.blockGameId IN (:...ids)', { ids: blockGameIds })
              .groupBy('sentence.blockGameId')
              .getRawMany()
          : [];

      const sentenceCountMap = sentenceCounts.reduce((map, item) => {
        map[item.blockGameId] = parseInt(item.count);
        return map;
      }, {});

      const blockGameDtos = blockGames.map((blockGame) => ({
        ...this.mapToResponseDto(blockGame),
        sentence_count: sentenceCountMap[blockGame.id] || 0,
      }));

      return new PagedListDto<BlockGameResponseDto>(blockGameDtos, totalCount);
    } catch (error) {
      this.logger.error(`Failed to get block games: ${error.message}`, error.stack);
      throw new BadRequestException('Could not retrieve block games at this time. Please try again later.');
    }
  }

  /**
   * Create a new block game
   */
  async createBlockGame(dto: CreateBlockGameDto): Promise<BlockGameResponseDto> {
    // Validate input
    if (dto.score <= 0) {
      this.logger.warn(`Invalid score: ${dto.score}`);
      throw new BadRequestException('Score must be greater than 0');
    }

    // Check if a block game with the same title already exists
    const existingBlockGame = await this.blockGameRepository.findOne({ where: { title: dto.title } });
    if (existingBlockGame) {
      this.logger.warn(`Attempted to create a duplicate block game with title: "${dto.title}"`);
      throw new BadRequestException(`A block game with title "${dto.title}" already exists`);
    }

    // Validate sentences with partial validation
    const { validSentences, errors } = this.validateSentencesPartial(dto.sentences);

    if (validSentences.length === 0) {
      const errorDetails = errors.reduce(
        (acc, error) => {
          acc[`sentence${error.index}`] = error.errors;
          return acc;
        },
        {} as Record<string, string[]>,
      );

      this.logger.warn(`No valid sentences to create for block game: "${dto.title}". Errors: ${JSON.stringify(errorDetails)}`);

      throw new BadRequestException({
        message: 'No valid sentences to create',
        error: {
          type: 'ValidationError',
          details: errorDetails,
        },
      });
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create block game
      const blockGame = queryRunner.manager.create(BlockGame, {
        title: dto.title,
        score: dto.score,
        isActive: true,
      });

      const savedBlockGame = await queryRunner.manager.save(blockGame);

      // Create sentences using only valid sentences with sequential order
      const sentences = validSentences.map((sentenceDto, index) =>
        queryRunner.manager.create(BlockGameSentence, {
          blockGameId: savedBlockGame.id,
          startingPart: sentenceDto.starting_part.trim(),
          expandingPart: sentenceDto.expanding_part.trim(),
          sentenceOrder: index + 1, // Sequential order: 1, 2, 3...
        }),
      );

      await queryRunner.manager.save(sentences);

      // Handle partial success before committing the transaction
      if (errors.length > 0) {
        // Commit the transaction first to save the valid sentences
        await queryRunner.commitTransaction();

        const errorDetails = errors.reduce(
          (acc: Record<string, string[]>, error: SentenceValidationError) => {
            acc[`sentence${error.index}`] = error.errors;
            return acc;
          },
          {} as Record<string, string[]>,
        );

        this.logger.warn(`Partial success: ${validSentences.length}/${dto.sentences.length} sentences created. Errors: ${JSON.stringify(errorDetails)}`);

        // Return a BadRequestException for partial success
        return Promise.reject(
          new BadRequestException({
            message: `Created ${validSentences.length} sentences successfully, but ${errors.length} sentences had validation errors`,
            error: {
              type: 'PartialValidationError',
              details: errorDetails,
            },
            partialSuccess: {
              successCount: validSentences.length,
              totalCount: dto.sentences.length,
              blockGameId: savedBlockGame.id,
            },
            invalidSentences: errors.map((error) => ({
              index: error.index,
              starting_part: error.sentence.starting_part,
              expanding_part: error.sentence.expanding_part,
              sentence_order: error.sentence.sentence_order,
              errors: error.errors,
            })),
          }),
        );
      }

      await queryRunner.commitTransaction();

      // Return the created block game with sentences
      const result = await this.blockGameRepository.findOne({
        where: { id: savedBlockGame.id },
        relations: ['sentences'],
      });

      return {
        ...this.mapToResponseDto(result),
        sentence_count: result.sentences ? result.sentences.length : 0,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to create block game: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Get a block game by ID
   */
  async getBlockGameById(id: string): Promise<BlockGameResponseDto> {
    try {
      // Use select to avoid loading unnecessary data for read-only operation
      const blockGame = await this.blockGameRepository.findOne({
        where: { id },
        select: ['id', 'title', 'score', 'isActive', 'createdAt', 'updatedAt', 'createdBy'],
      });

      if (!blockGame) {
        throw new NotFoundException('Block game not found');
      }

      // Get sentence count separately for better performance
      const sentenceCount = await this.blockGameSentenceRepository.count({
        where: { blockGameId: id },
      });

      return {
        ...this.mapToResponseDto(blockGame),
        sentence_count: sentenceCount,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to get block game by ID ${id}: ${error.message}`, error.stack);
      throw new BadRequestException('Could not retrieve block game at this time. Please try again later.');
    }
  }

  /**
   * Update a block game
   */
  async updateBlockGame(id: string, dto: UpdateBlockGameDto): Promise<BlockGameResponseDto> {
    const blockGame = await this.blockGameRepository.findOne({
      where: { id },
      relations: ['sentences'],
    });

    if (!blockGame) {
      throw new NotFoundException('Block game not found');
    }

    // Check if the block game has any participation records
    const hasParticipation = await this.hasParticipation(id);
    if (hasParticipation && (dto.sentences || dto.score !== undefined)) {
      this.logger.warn(`Attempted to update block game with ID ${id} that has participation records`);
      throw new ForbiddenException('Cannot modify game content or scoring because it has student participation records. You can only change the active status.');
    }

    // Validate input
    if (dto.score !== undefined && dto.score <= 0) {
      this.logger.warn(`Invalid score: ${dto.score}`);
      throw new BadRequestException('Score must be greater than 0');
    }

    // Validate sentences if provided
    if (dto.sentences) {
      this.validateSentences(dto.sentences);
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Update block game properties
      if (dto.title !== undefined) blockGame.title = dto.title;
      if (dto.score !== undefined) blockGame.score = dto.score;
      if (dto.is_active !== undefined) blockGame.isActive = dto.is_active;

      await queryRunner.manager.save(blockGame);

      // Update sentences if provided
      if (dto.sentences) {
        // Delete existing sentences
        await queryRunner.manager.delete(BlockGameSentence, { blockGameId: id });

        // Create new sentences
        const sentences = dto.sentences.map((sentenceDto, index) =>
          queryRunner.manager.create(BlockGameSentence, {
            blockGameId: id,
            startingPart: sentenceDto.starting_part.trim(),
            expandingPart: sentenceDto.expanding_part.trim(),
            sentenceOrder: sentenceDto.sentence_order || index + 1,
          }),
        );

        await queryRunner.manager.save(sentences);
      }

      await queryRunner.commitTransaction();

      // Return the updated block game
      const result = await this.blockGameRepository.findOne({
        where: { id },
        relations: ['sentences'],
      });

      return {
        ...this.mapToResponseDto(result),
        sentence_count: result.sentences ? result.sentences.length : 0,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to update block game with ID ${id}: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Delete a block game
   */
  async deleteBlockGame(id: string): Promise<void> {
    const blockGame = await this.blockGameRepository.findOne({ where: { id } });

    if (!blockGame) {
      throw new NotFoundException('Block game not found');
    }

    // Check if the block game has any participation records
    const hasParticipation = await this.hasParticipation(id);
    if (hasParticipation) {
      this.logger.warn(`Attempted to delete block game with ID ${id} that has participation records`);
      throw new ForbiddenException('Cannot delete this game because it has student participation records. You can deactivate it instead.');
    }

    try {
      await this.blockGameRepository.remove(blockGame);
    } catch (error) {
      this.logger.error(`Failed to delete block game with ID ${id}: ${error.message}`, error.stack);
      throw new BadRequestException('Could not delete this game. Please try again later.');
    }
  }

  /**
   * Toggle block game active status
   */
  async toggleBlockGameStatus(id: string, dto: ToggleBlockGameStatusDto): Promise<BlockGameResponseDto> {
    const blockGame = await this.blockGameRepository.findOne({
      where: { id },
      relations: ['sentences'],
    });

    if (!blockGame) {
      throw new NotFoundException('Block game not found');
    }

    try {
      // Update only the active status
      blockGame.isActive = dto.is_active;
      await this.blockGameRepository.save(blockGame);

      this.logger.log(`Block game with ID ${id} status changed to ${dto.is_active ? 'active' : 'inactive'}`);

      return {
        ...this.mapToResponseDto(blockGame),
        sentence_count: blockGame.sentences ? blockGame.sentences.length : 0,
      };
    } catch (error) {
      this.logger.error(`Failed to toggle block game status with ID ${id}: ${error.message}`, error.stack);
      throw new BadRequestException('Could not update game status. Please try again later.');
    }
  }

  /**
   * Check if a block game has any participation records
   */
  private async hasParticipation(blockGameId: string): Promise<boolean> {
    const count = await this.blockGameAttemptRepository.count({
      where: { blockGameId },
    });
    return count > 0;
  }

  /**
   * Validate a single sentence
   */
  private validateSentence(sentence: any, _index: number): string[] {
    const errors: string[] = [];

    if (!sentence.starting_part || !sentence.starting_part.trim()) {
      errors.push('Starting part is required');
    }

    if (!sentence.expanding_part || !sentence.expanding_part.trim()) {
      errors.push('Expanding part is required');
    }

    // Check word count (max 10 words for complete sentence)
    if (sentence.starting_part && sentence.expanding_part) {
      const startingWords = sentence.starting_part.trim().split(/\s+/);
      const expandingWords = sentence.expanding_part.trim().split(/\s+/);
      const totalWords = startingWords.length + expandingWords.length;

      if (totalWords > 10) {
        errors.push(`Complete sentence cannot exceed 10 words (currently ${totalWords} words)`);
      }
    }

    return errors;
  }

  /**
   * Validate sentences with partial validation (like waterfall)
   */
  private validateSentencesPartial(sentences: any[]): SentencesValidationResult {
    if (!sentences || sentences.length === 0) {
      throw new BadRequestException('At least one sentence must be provided');
    }

    if (sentences.length > 10) {
      throw new BadRequestException('Maximum 10 sentences are allowed');
    }

    const validSentences: any[] = [];
    const errors: SentenceValidationError[] = [];

    sentences.forEach((sentence, index) => {
      const sentenceErrors = this.validateSentence(sentence, index);

      if (sentenceErrors.length === 0) {
        validSentences.push(sentence);
      } else {
        errors.push({
          index,
          sentence,
          errors: sentenceErrors,
        });
      }
    });

    return { validSentences, errors };
  }

  /**
   * Validate sentences (legacy method for update - still throws on any error)
   */
  private validateSentences(sentences: any[]): void {
    const { errors } = this.validateSentencesPartial(sentences);

    if (errors.length > 0) {
      const firstError = errors[0];
      throw new BadRequestException(`Sentence ${firstError.index + 1}: ${firstError.errors[0]}`);
    }
  }

  /**
   * Get participants for all block games
   */
  async getParticipants(queryDto: GetBlockGameParticipantsQueryDto): Promise<BlockGameParticipantsResponseDto> {
    const { page = 1, limit = 10, search, sortBy = 'submittedAt', sortDirection = 'DESC' } = queryDto;

    const skip = (page - 1) * limit;

    try {
      const queryBuilder = this.userRepository
        .createQueryBuilder('user')
        .innerJoin(BlockGameAttempt, 'attempt', 'attempt.studentId = user.id')
        .innerJoin(BlockGame, 'blockGame', 'attempt.blockGameId = blockGame.id')
        .select([
          'user.id as student_id',
          'user.name as student_name',
          'user.email as student_email',
          'user.profilePicture as student_profile_picture',
          'COUNT(DISTINCT attempt.id) as total_attempts',
          'COALESCE(SUM(attempt.score), 0) as total_score',
          'MAX(attempt.submittedAt) as last_attempt_date',
        ])
        .where('user.type = :userType', { userType: UserType.STUDENT })
        .groupBy('user.id, user.name, user.email, user.profilePicture');

      // Apply search filter
      if (search) {
        queryBuilder.andWhere('(user.name ILIKE :search OR user.email ILIKE :search)', { search: `%${search}%` });
      }

      // Apply block game filter
      // if (block_game_id) {
      //   queryBuilder.andWhere('blockGame.id = :blockGameId', { blockGameId: block_game_id });
      // }

      // Apply sorting
      const sortFieldMapping = {
        name: 'user.name',
        totalAttempts: 'total_attempts',
        totalScore: 'total_score',
        submittedAt: 'last_attempt_date',
      };
      const validSortBy = sortFieldMapping[sortBy] || 'last_attempt_date';

      queryBuilder.orderBy(validSortBy, sortDirection as 'ASC' | 'DESC');

      // Get total count first
      const totalCount = await queryBuilder.getCount();

      // Apply pagination
      queryBuilder.limit(limit).offset(skip);

      // Execute the query
      const participants = await queryBuilder.getRawMany();

      // Map the results to the DTO format
      const participantDtos: BlockGameParticipantDto[] = participants.map((p) => ({
        student_id: p.student_id,
        student_name: p.student_name,
        student_email: p.student_email,
        student_profile_picture: p.student_profile_picture,
        total_attempts: parseInt(p.total_attempts),
        total_score: parseFloat(p.total_score),
        last_attempt_date: new Date(p.last_attempt_date),
      }));

      const totalPages = Math.ceil(totalCount / limit);

      return {
        participants: participantDtos,
        total_items: totalCount,
        total_pages: totalPages,
        current_page: page,
        page_size: limit,
      };
    } catch (error) {
      this.logger.error(`Failed to get participants: ${error.message}`, error.stack);
      throw new BadRequestException('Could not retrieve participants at this time. Please try again later.');
    }
  }

  /**
   * Get student participation history
   */
  async getStudentParticipation(queryDto: GetStudentBlockGameParticipationQueryDto): Promise<StudentBlockGameParticipationResponseDto> {
    const { student_id, page = 1, limit = 10, sortBy = 'submittedAt', sortDirection = 'DESC' } = queryDto;

    const skip = (page - 1) * limit;

    try {
      // Check if student exists
      const student = await this.userRepository.findOne({
        where: { id: student_id, type: UserType.STUDENT },
      });

      if (!student) {
        throw new NotFoundException('Student not found');
      }

      const queryBuilder = this.blockGameAttemptRepository
        .createQueryBuilder('attempt')
        .innerJoin('attempt.blockGame', 'blockGame')
        .select([
          'attempt.id as attempt_id',
          'attempt.blockGameId as block_game_id',
          'blockGame.title as block_game_title',
          'attempt.score as score',
          'attempt.totalScore as total_score',
          'attempt.submittedAt as submitted_at',
        ])
        .where('attempt.studentId = :studentId', { studentId: student_id });

      // Apply sorting
      const sortFieldMapping = {
        title: 'blockGame.title',
        score: 'attempt.score',
        submittedAt: 'attempt.submittedAt',
      };
      const validSortBy = sortFieldMapping[sortBy] || 'attempt.submittedAt';

      queryBuilder.orderBy(validSortBy, sortDirection as 'ASC' | 'DESC');

      // Get total count first
      const totalCount = await queryBuilder.getCount();

      // Apply pagination
      queryBuilder.limit(limit).offset(skip);

      // Execute the query
      const participations = await queryBuilder.getRawMany();

      // Map the results to the DTO format
      const participationDtos: StudentBlockGameParticipationDto[] = participations.map((p) => ({
        attempt_id: p.attempt_id,
        block_game_id: p.block_game_id,
        block_game_title: p.block_game_title,
        score: parseFloat(p.score),
        total_score: parseFloat(p.total_score),
        submitted_at: new Date(p.submitted_at),
      }));

      const totalPages = Math.ceil(totalCount / limit);

      return {
        participations: participationDtos,
        total_items: totalCount,
        total_pages: totalPages,
        current_page: page,
        page_size: limit,
      };
    } catch (error) {
      this.logger.error(`Failed to get participation history for student ${student_id}: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException('Could not retrieve student participation history at this time. Please try again later.');
    }
  }

  /**
   * Map entity to response DTO
   */
  private mapToResponseDto(blockGame: BlockGame): Omit<BlockGameResponseDto, 'sentence_count'> {
    return {
      id: blockGame.id,
      title: blockGame.title,
      score: blockGame.score,
      is_active: blockGame.isActive,
      created_at: blockGame.createdAt,
      updated_at: blockGame.updatedAt,
      created_by: blockGame.createdBy || 'Unknown',
    };
  }
}
